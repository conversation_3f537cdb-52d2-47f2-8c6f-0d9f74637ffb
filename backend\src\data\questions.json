{"questoes": [{"id": 2001, "pergunta": "Qual é o principal objetivo do Sistema de Valor de Serviço (SVS) do ITIL 4?", "alternativas": ["Facilitar a criação de valor através de serviços habilitados por TI", "Reduzir custos operacionais da organização", "Implementar processos de governança de TI", "Automatizar todos os serviços de TI"], "resposta_correta": 0, "categoria": "conceitos_fundamentais", "subcategoria": "sistema_valor_servico", "dificuldade": "facil", "explicacao": "O SVS representa como todos os componentes e atividades da organização trabalham juntos para facilitar a criação de valor através de serviços habilitados por TI.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 2002, "pergunta": "Quantos são os princípios orientadores do ITIL 4?", "alternativas": ["5 princípios", "6 princí<PERSON>s", "7 princípios", "8 princípios"], "resposta_correta": 2, "categoria": "principios_orientadores", "subcategoria": "geral", "dificuldade": "facil", "explicacao": "O ITIL 4 possui 7 princípios orientadores que guiam as decisões e ações da organização.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 2003, "pergunta": "Qual das seguintes NÃO é uma das quatro dimensões do gerenciamento de serviços?", "alternativas": ["Organizações e pessoas", "Informação e tecnologia", "Parceiros e fornecedores", "Custos e benefícios"], "resposta_correta": 3, "categoria": "conceitos_fundamentais", "subcategoria": "quatro_dimensoes", "dificuldade": "media", "explicacao": "As quatro dimensões são: Organizações e pessoas, Informação e tecnologia, Parceiros e fornecedores, e Fluxos de valor e processos.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 2004, "pergunta": "O que significa 'Utilidade' no contexto de serviços ITIL 4?", "alternativas": ["Como o serviço é entregue", "O que o serviço faz", "Quando o serviço está disponível", "Onde o serviço é executado"], "resposta_correta": 1, "categoria": "conceitos_fundamentais", "subcategoria": "valor_servicos", "dificuldade": "media", "explicacao": "Utilidade refere-se à funcionalidade oferecida por um produto ou serviço para atender a uma necessidade específica - ou seja, 'o que o serviço faz'.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 2005, "pergunta": "Qual prática é responsável por minimizar o impacto negativo de incidentes?", "alternativas": ["Gerenciamento de Problemas", "Gerenciamento de Incidentes", "Gerenciamento de Mudanças", "Central de Serviços"], "resposta_correta": 1, "categoria": "praticas_gerenciamento", "subcategoria": "praticas_gerais", "dificuldade": "facil", "explicacao": "O Gerenciamento de Incidentes tem como objetivo minimizar o impacto negativo de incidentes restaurando a operação normal do serviço o mais rápido possível.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3001, "pergunta": "Qual é a definição de 'Serviço' no ITIL 4?", "alternativas": ["Um meio de entregar valor aos clientes facilitando os resultados que os clientes querem alcançar", "Um conjunto de processos de TI automatizados", "Uma aplicação de software que suporta processos de negócio", "Um contrato entre provedor e cliente de serviços"], "resposta_correta": 0, "categoria": "conceitos_fundamentais", "subcategoria": "definicoes_basicas", "dificuldade": "facil", "explicacao": "Um serviço é um meio de entregar valor aos clientes, facilitando os resultados que os clientes querem alcançar sem que eles tenham que gerenciar custos e riscos específicos.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3002, "pergunta": "O que é 'Garantia' no contexto de valor de serviço?", "alternativas": ["A funcionalidade oferecida pelo serviço", "A segurança dos dados do cliente", "Como o serviço é entregue (disponibilidade, capacidade, segurança, continuidade)", "O contrato de nível de serviço"], "resposta_correta": 2, "categoria": "conceitos_fundamentais", "subcategoria": "valor_servicos", "dificuldade": "media", "explicacao": "Garantia refere-se a como o serviço é entregue, incluindo aspectos como disponibilidade, capacidade, segurança e continuidade do serviço.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3003, "pergunta": "Quais são os componentes principais do Sistema de Valor de Serviço (SVS)?", "alternativas": ["Processos, funções e papéis", "Princípios orientadores, governança, cadeia de valor, práticas, melhoria contínua", "Incidentes, problemas e mudanças", "Pessoas, processos e tecnologia"], "resposta_correta": 1, "categoria": "conceitos_fundamentais", "subcategoria": "sistema_valor_servico", "dificuldade": "media", "explicacao": "O SVS inclui os princípios orientadores, governança, cadeia de valor de serviço, práticas e melhoria contínua.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3004, "pergunta": "Qual princípio orientador enfatiza que 'tudo que a organização faz deve mapear para valor'?", "alternativas": ["Começar de onde você está", "Foco no valor", "Progredir iterativamente com feedback", "Colaborar e promover visibilidade"], "resposta_correta": 1, "categoria": "principios_orientadores", "subcategoria": "foco_valor", "dificuldade": "facil", "explicacao": "O princípio 'Foco no valor' estabelece que tudo que a organização faz deve mapear, direta ou indiretamente, para valor para as partes interessadas.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3005, "pergunta": "O princípio 'Começar de onde você está' significa:", "alternativas": ["Sempre implementar soluções completamente novas", "O<PERSON>ervar o que já existe e construir sobre isso", "<PERSON><PERSON><PERSON> sistemas legados", "Começar com orçamento zero"], "resposta_correta": 1, "categoria": "principios_orientadores", "subcategoria": "comecar_onde_esta", "dificuldade": "media", "explicacao": "Este princípio enfatiza não começar do zero, mas observar o que já existe e construir sobre isso, preservando o que é valioso.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3006, "pergunta": "Qual princípio orientador sugere 'usar o número mínimo de etapas para atingir um objetivo'?", "alternativas": ["Otimizar e automatizar", "Manter simples e prático", "Pensar e trabalhar holisticamente", "Progredir iterativamente com feedback"], "resposta_correta": 1, "categoria": "principios_orientadores", "subcategoria": "simples_pratico", "dificuldade": "media", "explicacao": "O princípio 'Manter simples e prático' enfatiza usar o número mínimo de etapas para atingir um objetivo e eliminar atividades que não agregam valor.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3007, "pergunta": "A dimensão 'Organizações e pessoas' inclui:", "alternativas": ["Apenas a estrutura organizacional", "Estrutura organizacional, cultura, papéis, responsabilidades e competências", "Somente recursos humanos", "Apenas processos de negócio"], "resposta_correta": 1, "categoria": "conceitos_fundamentais", "subcategoria": "quatro_dimensoes", "dificuldade": "media", "explicacao": "A dimensão 'Organizações e pessoas' abrange estrutura organizacional, cultura, papéis, responsabilidades, competências e habilidades necessárias.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3008, "pergunta": "Qual dimensão se concentra em 'como as várias partes da organização trabalham de forma integrada'?", "alternativas": ["Organizações e pessoas", "Informação e tecnologia", "Parceiros e fornecedores", "Fluxos de valor e processos"], "resposta_correta": 3, "categoria": "conceitos_fundamentais", "subcategoria": "quatro_dimensoes", "dificuldade": "media", "explicacao": "A dimensão 'Fluxos de valor e processos' foca em como as várias partes da organização trabalham de forma integrada e coordenada para entregar valor.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3009, "pergunta": "Qual é o objetivo principal da Central de Serviços?", "alternativas": ["Resolver todos os problemas de TI", "Ser o ponto único de contato entre provedor e usuários", "Gerenciar mudanças no ambiente", "Monitorar a performance dos serviços"], "resposta_correta": 1, "categoria": "praticas_gerenciamento", "subcategoria": "central_servicos", "dificuldade": "facil", "explicacao": "A Central de Serviços é o ponto único de contato entre o provedor de serviços e os usuários para questões, consultas e requisições de serviço.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3010, "pergunta": "Qual é a diferença principal entre Incidente e Problema?", "alternativas": ["Não há diferença, são sinônimos", "Incidente é uma interrupção não planejada; Problema é a causa de um ou mais incidentes", "Problema é mais urgente que Incidente", "Incidente afeta usuários; Problema afeta sistemas"], "resposta_correta": 1, "categoria": "praticas_gerenciamento", "subcategoria": "incidentes_problemas", "dificuldade": "media", "explicacao": "Um incidente é uma interrupção não planejada ou redução na qualidade de um serviço. Um problema é a causa de um ou mais incidentes.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3011, "pergunta": "Quantas atividades da cadeia de valor existem no ITIL 4?", "alternativas": ["5 atividades", "6 atividades", "7 atividades", "8 atividades"], "resposta_correta": 1, "categoria": "conceitos_fundamentais", "subcategoria": "cadeia_valor", "dificuldade": "facil", "explicacao": "A cadeia de valor de serviço do ITIL 4 possui 6 atividades: <PERSON><PERSON>ar, <PERSON><PERSON><PERSON>, <PERSON><PERSON>jar, Design e transição, Obter/construir, Entregar e suportar.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3012, "pergunta": "Qual atividade da cadeia de valor garante entendimento compartilhado da visão?", "alternativas": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Design e transição"], "resposta_correta": 1, "categoria": "conceitos_fundamentais", "subcategoria": "cadeia_valor", "dificuldade": "media", "explicacao": "A atividade 'Planejar' garante entendimento compartilhado da visão, status atual e direção de melhoria para todos os produtos e serviços.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3013, "pergunta": "Qual é a primeira pergunta do modelo de melhoria contínua?", "alternativas": ["Onde estamos agora?", "Qual é a visão?", "Como chegamos lá?", "Onde queremos estar?"], "resposta_correta": 1, "categoria": "melhoria_continua", "subcategoria": "modelo_melhoria", "dificuldade": "media", "explicacao": "O modelo de melhoria contínua começa com 'Qual é a visão?' para estabelecer a direção e objetivos da melhoria.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3014, "pergunta": "O que é o Registro de Melhoria Contínua (RMC)?", "alternativas": ["Um relatório de incidentes", "Um banco de dados para registrar oportunidades de melhoria", "Um sistema de monitoramento", "Um processo de aprovação de mudanças"], "resposta_correta": 1, "categoria": "melhoria_continua", "subcategoria": "ferramentas", "dificuldade": "media", "explicacao": "O RMC é um banco de dados ou lista estruturada usada para registrar e gerenciar oportunidades de melhoria ao longo de seu ciclo de vida.", "simulados": ["ITIL4_FOUNDATION"], "idioma": "pt-br", "fonte": "ITIL 4 Foundation Official"}, {"id": 3000, "pergunta": "What is the purpose of the 'service desk' practice?", "alternativas": ["To capture demand for incident resolution and service requests", "To reduce the likelihood and impact of incidents by identifying actual and potential causes", "To maximize the number of successful IT changes by ensuring risks are properly assessed", "To set clear business-based targets for service levels"], "resposta_correta": 0, "categoria": "praticas_gerenciamento", "subcategoria": "central_servico", "dificuldade": "facil", "explicacao": "The service desk practice serves as the single point of contact between the service provider and users. Its purpose is to capture demand for incident resolution and service requests.", "simulados": ["ITIL_4_ENGLISH_COLLECTION"], "idioma": "en"}, {"id": 4000, "pergunta": "¿Cuál es el propósito de la práctica 'mesa de servicios'?", "alternativas": ["Capturar la demanda de resolución de incidentes y solicitudes de servicio", "Reducir la probabilidad e impacto de incidentes identificando causas reales y potenciales", "Maximizar el número de cambios exitosos de TI asegurando que los riesgos sean evaluados adecuadamente", "Establecer objetivos claros basados en el negocio para los niveles de servicio"], "resposta_correta": 0, "categoria": "praticas_gerenciamento", "subcategoria": "central_servicos", "dificuldade": "facil", "explicacao": "La práctica de mesa de servicios sirve como el punto único de contacto entre el proveedor de servicios y los usuarios. Su propósito es capturar la demanda de resolución de incidentes y solicitudes de servicio.", "simulados": ["ITIL_4_SPANISH_COLLECTION"], "idioma": "es", "fonte": "ITIL 4 Foundation Official"}, {"id": 4001, "pergunta": "¿Cuál es el objetivo principal del Sistema de Valor de Servicio (SVS) de ITIL 4?", "alternativas": ["Facilitar la creación de valor a través de servicios habilitados por TI", "Reducir los costos operacionales de la organización", "Implementar procesos de gobernanza de TI", "Automatizar todos los servicios de TI"], "resposta_correta": 0, "categoria": "conceitos_fundamentais", "subcategoria": "sistema_valor_servico", "dificuldade": "facil", "explicacao": "El SVS representa cómo todos los componentes y actividades de la organización trabajan juntos para facilitar la creación de valor a través de servicios habilitados por TI.", "simulados": ["ITIL_4_SPANISH_COLLECTION"], "idioma": "es", "fonte": "ITIL 4 Foundation Official"}, {"id": 4002, "pergunta": "¿Cuántos son los principios rectores de ITIL 4?", "alternativas": ["5 principios", "6 principios", "7 principios", "8 principios"], "resposta_correta": 2, "categoria": "principios_orientadores", "subcategoria": "geral", "dificuldade": "facil", "explicacao": "ITIL 4 tiene 7 principios rectores que guían las decisiones y acciones de la organización.", "simulados": ["ITIL_4_SPANISH_COLLECTION"], "idioma": "es", "fonte": "ITIL 4 Foundation Official"}, {"id": 4003, "pergunta": "¿Cuál de las siguientes NO es una de las cuatro dimensiones de la gestión de servicios?", "alternativas": ["Organizaciones y personas", "Información y tecnología", "Socios y proveedores", "Costos y beneficios"], "resposta_correta": 3, "categoria": "conceitos_fundamentais", "subcategoria": "quatro_dimensoes", "dificuldade": "media", "explicacao": "Las cuatro dimensiones son: Organizaciones y personas, Información y tecnología, Socios y proveedores, y Flujos de valor y procesos.", "simulados": ["ITIL_4_SPANISH_COLLECTION"], "idioma": "es", "fonte": "ITIL 4 Foundation Official"}, {"id": 4004, "pergunta": "¿Qué significa 'Utilidad' en el contexto de servicios ITIL 4?", "alternativas": ["Cómo se entrega el servicio", "Lo que hace el servicio", "Cuándo está disponible el servicio", "Dónde se ejecuta el servicio"], "resposta_correta": 1, "categoria": "conceitos_fundamentais", "subcategoria": "valor_servicos", "dificuldade": "media", "explicacao": "La utilidad se refiere a la funcionalidad ofrecida por un producto o servicio para satisfacer una necesidad específica - es decir, 'lo que hace el servicio'.", "simulados": ["ITIL_4_SPANISH_COLLECTION"], "idioma": "es", "fonte": "ITIL 4 Foundation Official"}, {"id": 4005, "pergunta": "¿Qué práctica es responsable de minimizar el impacto negativo de los incidentes?", "alternativas": ["Gestión de Problemas", "Gestión de Incidentes", "Gestión de Cambios", "Mesa de Servicios"], "resposta_correta": 1, "categoria": "praticas_gerenciamento", "subcategoria": "praticas_gerais", "dificuldade": "facil", "explicacao": "La Gestión de Incidentes tiene como objetivo minimizar el impacto negativo de los incidentes restaurando la operación normal del servicio lo más rápido posible.", "simulados": ["ITIL_4_SPANISH_COLLECTION"], "idioma": "es", "fonte": "ITIL 4 Foundation Official"}, {"id": 4006, "pergunta": "¿Cuál es la definición de 'Servicio' en ITIL 4?", "alternativas": ["Un medio de entregar valor a los clientes facilitando los resultados que los clientes quieren lograr", "Un conjunto de procesos de TI automatizados", "Una aplicación de software que soporta procesos de negocio", "Un contrato entre proveedor y cliente de servicios"], "resposta_correta": 0, "categoria": "conceitos_fundamentais", "subcategoria": "definicoes_basicas", "dificuldade": "facil", "explicacao": "Un servicio es un medio de entregar valor a los clientes, facilitando los resultados que los clientes quieren lograr sin que tengan que gestionar costos y riesgos específicos.", "simulados": ["ITIL_4_SPANISH_COLLECTION"], "idioma": "es", "fonte": "ITIL 4 Foundation Official"}, {"id": 4007, "pergunta": "¿Qué es 'Garantía' en el contexto de valor de servicio?", "alternativas": ["La funcionalidad ofrecida por el servicio", "La seguridad de los datos del cliente", "Cómo se entrega el servicio (disponibilidad, capacidad, seguridad, continuidad)", "El acuerdo de nivel de servicio"], "resposta_correta": 2, "categoria": "conceitos_fundamentais", "subcategoria": "valor_servicos", "dificuldade": "media", "explicacao": "La garantía se refiere a cómo se entrega el servicio, incluyendo aspectos como disponibilidad, capacidad, seguridad y continuidad del servicio.", "simulados": ["ITIL_4_SPANISH_COLLECTION"], "idioma": "es", "fonte": "ITIL 4 Foundation Official"}, {"id": 4008, "pergunta": "¿<PERSON>uáles son los componentes principales del Sistema de Valor de Servicio (SVS)?", "alternativas": ["Procesos, funciones y roles", "Principios rectores, gobernanza, cadena de valor, prácticas, mejora continua", "Incidentes, problemas y cambios", "Personas, procesos y tecnología"], "resposta_correta": 1, "categoria": "conceitos_fundamentais", "subcategoria": "sistema_valor_servico", "dificuldade": "media", "explicacao": "El SVS incluye los principios rectores, gobernanza, cadena de valor de servicio, prácticas y mejora continua.", "simulados": ["ITIL_4_SPANISH_COLLECTION"], "idioma": "es", "fonte": "ITIL 4 Foundation Official"}, {"id": 4009, "pergunta": "¿Qué principio rector enfatiza que 'todo lo que hace la organización debe mapear a valor'?", "alternativas": ["Comenzar donde estás", "Enfoque en el valor", "Progresar iterativamente con retroalimentación", "Colaborar y promover visibilidad"], "resposta_correta": 1, "categoria": "principios_orientadores", "subcategoria": "foco_valor", "dificuldade": "facil", "explicacao": "El principio 'Enfoque en el valor' establece que todo lo que hace la organización debe mapear, directa o indirectamente, a valor para las partes interesadas.", "simulados": ["ITIL_4_SPANISH_COLLECTION"], "idioma": "es", "fonte": "ITIL 4 Foundation Official"}, {"id": 3024, "pergunta": "How many activities are there in the ITIL 4 service value chain?", "alternativas": ["5 activities", "6 activities", "7 activities", "8 activities"], "resposta_correta": 1, "categoria": "conceitos_fundamentais", "subcategoria": "cadeia_valor", "dificuldade": "facil", "explicacao": "The ITIL 4 service value chain has 6 activities: Plan, Improve, Engage, Design and transition, Obtain/build, Deliver and support.", "simulados": ["ITIL_4_ENGLISH_COLLECTION"], "idioma": "en", "fonte": "ITIL 4 Foundation Official"}, {"id": 3025, "pergunta": "Which value chain activity ensures shared understanding of the vision?", "alternativas": ["Engage", "Plan", "Improve", "Design and transition"], "resposta_correta": 1, "categoria": "conceitos_fundamentais", "subcategoria": "cadeia_valor", "dificuldade": "media", "explicacao": "The 'Plan' activity ensures shared understanding of the vision, current status and improvement direction for all products and services.", "simulados": ["ITIL_4_ENGLISH_COLLECTION"], "idioma": "en", "fonte": "ITIL 4 Foundation Official"}, {"id": 3026, "pergunta": "What is the main purpose of the Service Desk?", "alternativas": ["To solve all IT problems", "To be the single point of contact between provider and users", "To manage changes in the environment", "To monitor service performance"], "resposta_correta": 1, "categoria": "praticas_gerenciamento", "subcategoria": "central_servicos", "dificuldade": "facil", "explicacao": "The Service Desk is the single point of contact between the service provider and users for questions, queries and service requests.", "simulados": ["ITIL_4_ENGLISH_COLLECTION"], "idioma": "en", "fonte": "ITIL 4 Foundation Official"}, {"id": 3027, "pergunta": "What is the main difference between Incident and Problem?", "alternativas": ["There is no difference, they are synonymous", "Incident is an unplanned interruption; Problem is the cause of one or more incidents", "Problem is more urgent than Incident", "Incident affects users; Problem affects systems"], "resposta_correta": 1, "categoria": "praticas_gerenciamento", "subcategoria": "incidentes_problemas", "dificuldade": "media", "explicacao": "An incident is an unplanned interruption or reduction in service quality. A problem is the cause of one or more incidents.", "simulados": ["ITIL_4_ENGLISH_COLLECTION"], "idioma": "en", "fonte": "ITIL 4 Foundation Official"}], "categorias": [{"id": "conceitos_fundamentais", "nome": "Conceitos Fundamentais", "descricao": "Questões sobre conceitos fundamentais", "subcategorias": ["cadeia_valor", "valor_servicos", "quatro_dimensoes", "definicoes_basicas", "sistema_valor_servico"]}, {"id": "melhoria_continua", "nome": "<PERSON><PERSON><PERSON> Con<PERSON>ua", "descricao": "Questões sobre melhoria continua", "subcategorias": ["modelo_melhoria", "ferramentas"]}, {"id": "praticas_gerenciamento", "nome": "Praticas Gerenciamento", "descricao": "Questões sobre praticas gerenciamento", "subcategorias": ["central_servicos", "praticas_gerais", "incidentes_problemas"]}, {"id": "principios_orientadores", "nome": "Principios Orientadores", "descricao": "Questões sobre principios orientadores", "subcategorias": ["simples_pratico", "foco_valor", "comecar_onde_esta", "geral"]}], "simulados": [{"id": "ITIL4_FOUNDATION", "nome": "Itil4 Foundation", "total_questoes": 19, "descricao": "Simulado com 19 questões"}, {"id": "ITIL_4_ENGLISH_COLLECTION", "nome": "ITIL 4 English Collection", "total_questoes": 5, "descricao": "ITIL 4 questions in English for international certification preparation"}, {"id": "ITIL_4_SPANISH_COLLECTION", "nome": "ITIL 4 Colección Español", "total_questoes": 10, "descricao": "Preguntas ITIL 4 en español para preparación de certificación internacional"}]}