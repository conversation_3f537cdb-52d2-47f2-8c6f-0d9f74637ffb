#!/bin/bash

# Script de configuração inicial do projeto Simulado ITIL 4
# Este script automatiza a instalação de dependências e configuração inicial

set -e  # Sair em caso de erro

echo "🚀 Configurando o projeto Simulado ITIL 4..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir mensagens coloridas
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar se Node.js está instalado
check_nodejs() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js encontrado: $NODE_VERSION"
    else
        print_error "Node.js não encontrado. Por favor, instale Node.js 16+ antes de continuar."
        exit 1
    fi
}

# Verificar se Python está instalado
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        print_success "Python encontrado: $PYTHON_VERSION"
    elif command -v python &> /dev/null; then
        PYTHON_VERSION=$(python --version)
        print_success "Python encontrado: $PYTHON_VERSION"
    else
        print_error "Python não encontrado. Por favor, instale Python 3.8+ antes de continuar."
        exit 1
    fi
}

# Configurar frontend
setup_frontend() {
    print_status "Configurando frontend..."
    
    cd frontend
    
    # Verificar se package.json existe
    if [ ! -f "package.json" ]; then
        print_error "package.json não encontrado no diretório frontend"
        exit 1
    fi
    
    # Instalar dependências
    if command -v pnpm &> /dev/null; then
        print_status "Usando pnpm para instalar dependências..."
        pnpm install
    elif command -v yarn &> /dev/null; then
        print_status "Usando yarn para instalar dependências..."
        yarn install
    else
        print_status "Usando npm para instalar dependências..."
        npm install
    fi
    
    print_success "Frontend configurado com sucesso!"
    cd ..
}

# Configurar backend
setup_backend() {
    print_status "Configurando backend..."
    
    cd backend
    
    # Criar ambiente virtual se não existir
    if [ ! -d "venv" ]; then
        print_status "Criando ambiente virtual Python..."
        python3 -m venv venv 2>/dev/null || python -m venv venv
    fi
    
    # Ativar ambiente virtual
    print_status "Ativando ambiente virtual..."
    source venv/bin/activate
    
    # Atualizar pip
    print_status "Atualizando pip..."
    pip install --upgrade pip
    
    # Instalar dependências
    print_status "Instalando dependências Python..."
    pip install -r requirements.txt
    
    # Instalar dependências de desenvolvimento se o arquivo existir
    if [ -f "requirements-dev.txt" ]; then
        print_status "Instalando dependências de desenvolvimento..."
        pip install -r requirements-dev.txt
    fi
    
    # Criar arquivo .env se não existir
    if [ ! -f ".env" ] && [ -f ".env.example" ]; then
        print_status "Criando arquivo .env..."
        cp .env.example .env
        print_warning "Arquivo .env criado. Revise as configurações se necessário."
    fi
    
    print_success "Backend configurado com sucesso!"
    cd ..
}

# Executar verificações e configurações
main() {
    print_status "Iniciando configuração do projeto..."
    
    # Verificar pré-requisitos
    check_nodejs
    check_python
    
    # Configurar componentes
    setup_frontend
    setup_backend
    
    print_success "🎉 Projeto configurado com sucesso!"
    echo ""
    print_status "Próximos passos:"
    echo "1. Para executar o frontend: cd frontend && npm run dev"
    echo "2. Para executar o backend: cd backend && source venv/bin/activate && python src/main.py"
    echo "3. Para executar testes: npm run test (frontend) ou pytest (backend)"
    echo ""
    print_status "Acesse http://localhost:3000 para ver a aplicação"
}

# Executar função principal
main "$@"
