#!/usr/bin/env python3
"""
Script para extrair questões do Daypo ITIL 4 TODAS 07-2023.
Processa o conteúdo HTML/texto do Daypo e converte para formato JSON.
"""

import json
import re
import os
from typing import List, Dict
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DaypoQuestionExtractor:
    def __init__(self):
        self.questions = []
        self.question_id_counter = 1000  # Começar com ID alto para evitar conflitos
        
    def clean_text(self, text: str) -> str:
        """Limpa e normaliza texto."""
        if not text:
            return ""
        
        # Remove caracteres especiais e normaliza espaços
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Decodifica caracteres HTML/Unicode
        replacements = {
            '&amp;': '&', '&lt;': '<', '&gt;': '>', '&quot;': '"',
            '&#39;': "'", '&nbsp;': ' ',
            'ã': 'ã', 'á': 'á', 'à': 'à', 'â': 'â',
            'é': 'é', 'ê': 'ê', 'í': 'í', 'ó': 'ó', 'ô': 'ô', 'õ': 'õ',
            'ú': 'ú', 'ü': 'ü', 'ç': 'ç'
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        return text

    def categorize_question(self, question_text: str) -> tuple:
        """Categoriza uma questão baseada no conteúdo."""
        text_lower = question_text.lower()
        
        # Mapeamento de palavras-chave para categorias
        category_keywords = {
            'principios_orientadores': [
                'princípio', 'principio', 'orientador', 'foco no valor', 'colaborar', 
                'otimizar', 'automatizar', 'iterativamente', 'holístico', 'simples', 
                'prático', 'começar de onde', 'promover visibilidade'
            ],
            'quatro_dimensoes': [
                'dimensão', 'dimensao', 'organizações', 'pessoas', 'informação', 
                'tecnologia', 'parceiros', 'fornecedores', 'fluxos', 'processos'
            ],
            'praticas_gerenciamento': [
                'central de serviço', 'incidente', 'problema', 'requisição', 'mudança', 
                'liberação', 'configuração', 'nível de serviço', 'gerenciamento',
                'mesa de serviço', 'habilitação', 'implantação', 'fornecedor'
            ],
            'melhoria_continua': [
                'melhoria', 'continua', 'modelo', 'registro', 'swot', 'fofa'
            ]
        }
        
        for category, keywords in category_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return category, self.get_subcategory(category, text_lower)
        
        return 'conceitos_fundamentais', 'definicoes_basicas'

    def get_subcategory(self, category: str, text_lower: str) -> str:
        """Determina subcategoria baseada na categoria principal."""
        subcategory_map = {
            'principios_orientadores': {
                'foco': 'foco_valor',
                'começar': 'comecar_onde_esta',
                'iterativ': 'progredir_iterativamente',
                'colaborar': 'colaborar_promover_visibilidade',
                'holístic': 'pensar_trabalhar_holisticamente',
                'simples': 'manter_simples_pratico',
                'otimizar': 'otimizar_automatizar'
            },
            'quatro_dimensoes': {
                'organização': 'organizacoes_pessoas',
                'informação': 'informacao_tecnologia',
                'parceiros': 'parceiros_fornecedores',
                'fluxos': 'fluxos_valor_processos'
            },
            'praticas_gerenciamento': {
                'central': 'central_servico',
                'incidente': 'gerenciamento_incidente',
                'problema': 'gerenciamento_problema',
                'requisição': 'gerenciamento_requisicao_servico',
                'nível': 'gerenciamento_nivel_servico',
                'mudança': 'habilitacao_mudanca',
                'configuração': 'gerenciamento_configuracao_servico'
            },
            'melhoria_continua': {
                'modelo': 'modelo_melhoria_continua',
                'registro': 'registro_melhoria_continua'
            }
        }
        
        if category in subcategory_map:
            for keyword, subcategory in subcategory_map[category].items():
                if keyword in text_lower:
                    return subcategory
        
        # Subcategoria padrão por categoria
        defaults = {
            'principios_orientadores': 'foco_valor',
            'quatro_dimensoes': 'organizacoes_pessoas',
            'praticas_gerenciamento': 'central_servico',
            'melhoria_continua': 'modelo_melhoria_continua',
            'conceitos_fundamentais': 'definicoes_basicas'
        }
        
        return defaults.get(category, 'definicoes_basicas')

    def determine_difficulty(self, question_text: str) -> str:
        """Determina a dificuldade da questão."""
        text_lower = question_text.lower()
        
        # Palavras que indicam alta complexidade
        complex_keywords = [
            'análise', 'avaliação', 'implementação', 'estratégico', 'tático', 
            'operacional', 'integração', 'coordenação', 'otimização'
        ]
        
        # Palavras que indicam conceitos básicos
        basic_keywords = [
            'definição', 'conceito', 'propósito', 'objetivo', 'exemplo'
        ]
        
        if any(keyword in text_lower for keyword in complex_keywords):
            return 'dificil'
        elif any(keyword in text_lower for keyword in basic_keywords):
            return 'facil'
        elif len(question_text) > 150:
            return 'media'
        else:
            return 'facil'

    def extract_questions_from_daypo_content(self) -> List[Dict]:
        """Extrai questões do conteúdo do Daypo."""
        # Lista expandida das questões extraídas do Daypo ITIL 4 TODAS 07-2023
        daypo_questions_raw = [
            {
                "pergunta": "Qual princípio orientador ITIL recomenda o uso de serviços, processos e ferramentas existentes para melhorar os serviços?",
                "alternativas": ["Progredir interativamente com feedback", "Manter de forma simples e prática", "Comece de onde você está", "Foco no valor"],
                "resposta_correta": 2
            },
            {
                "pergunta": "Qual prática tem um objetivo que inclui garantir que os riscos sejam adequadamente avaliados?",
                "alternativas": ["Gerenciamento de configuração de serviço", "Gerenciamento de problemas", "Gerenciamento do nível de serviço", "Habilitação de mudanças"],
                "resposta_correta": 3
            },
            {
                "pergunta": "Quando se deve realizar uma avaliação de risco completa e a autorização para uma mudança padrão?",
                "alternativas": ["Sempre que a mudança padrão é implementada", "Quando o procedimento para a mudança padrão é criado", "No mínimo uma vez por ano", "Quando uma mudança de emergência é solicitada"],
                "resposta_correta": 1
            },
            {
                "pergunta": "Qual afirmação sobre mudanças emergenciais está CORRETA?",
                "alternativas": ["O teste de emergência pode ser eliminado para implementar a mudança rapidamente", "A avaliação e autorização de mudanças de emergência são agilizadas para garantir que elas possam ser implementadas", "Alterações de emergências devem ser autorizadas e implementadas como solicitação de serviço", "As mudanças emergenciais devem ser totalmente documentadas antes da autorização e implementação"],
                "resposta_correta": 1
            },
            {
                "pergunta": "Qual prática coordena a classificação, propriedade e comunicação das requisições de serviços e dos incidentes?",
                "alternativas": ["Gerenciamento de fornecedor", "Central de serviço", "Gerenciamento de problemas", "Gerenciamento de relacionamento"],
                "resposta_correta": 1
            },
            {
                "pergunta": "O que é Garantia?",
                "alternativas": ["Confirmação de que um produto ou serviço atenderá os requisitos acordados", "Quantia gasta em dinheiro em uma determinada atividade ou recurso", "Funcionalidade oferecida por um produto ou serviço para atender a uma necessidade específica", "Benefícios, utilidade e importância percebidos de algo"],
                "resposta_correta": 0
            },
            {
                "pergunta": "O que inclui o termo provisão de serviço?",
                "alternativas": ["O gerenciamento de recursos configurados para entregar o serviço", "O gerenciamento dos recursos necessários para consumir o serviço", "O agrupamento de um ou mais serviços com base em um ou mais produtos", "As atividades conjuntas realizadas para garantir a co-criação contínua de valor"],
                "resposta_correta": 0
            },
            {
                "pergunta": "Qual a afirmação sobre um 'registro de melhoria contínua (RMC)' é CORRETA?",
                "alternativas": ["Deve ser gerenciado no nível sênior da organização", "Deve ser usado para capturar a demanda do usuário", "Deve haver apenas um para toda a organização", "Deve ser priorizado à medida que as ideias forem documentadas"],
                "resposta_correta": 3
            },
            {
                "pergunta": "O que são exemplos de 'envolver', 'planejar' e 'melhorar'?",
                "alternativas": ["Atividades da cadeia de valor de serviço", "Gerenciamento de nível de serviço", "Entradas da cadeia de valor de serviço", "Habilitação de mudanças"],
                "resposta_correta": 0
            },
            {
                "pergunta": "Qual afirmativa sobre resultados é correta?",
                "alternativas": ["Um resultado pode ser ativado por mais de uma saída", "Os resultados são como o serviço executa", "Uma saída pode ser ativada por um ou mais resultados", "Um resultado é uma atividade tangível e intangível"],
                "resposta_correta": 0
            },
            {
                "pergunta": "Qual a afirmação sobre a Central de Serviço está CORRETA?",
                "alternativas": ["A central de serviço deve trabalhar em estreita colaboração com as equipes de suporte e desenvolvimento", "A central de serviço deve recorrer aos portais de autoatendimento, em vez de encaminhar para as equipes de suporte", "A central de serviço deve se manter isolada das equipes de suporte técnico", "A central de serviço deve encaminhar todas as questões técnicas para as equipes de suporte e desenvolvimento"],
                "resposta_correta": 0
            },
            {
                "pergunta": "Qual prática atualiza as informações relacionadas a sintomas e impacto nos negócios?",
                "alternativas": ["Gerenciamento do nível de serviço", "Habilitação de mudanças", "Gerenciamento de solicitação de serviço", "Gerenciamento de incidentes"],
                "resposta_correta": 3
            },
            {
                "pergunta": "O que está incluído no objetivo da atividade da cadeia de valor de 'desenho e transição'?",
                "alternativas": ["Garantir que os componentes de serviço estejam disponíveis quando necessário", "Proporcionar transparência e boas relações com as partes interessadas", "Serviços de suporte de acordo com as especificações", "Atender continuamente às expectativas das partes interessadas em relação aos custos"],
                "resposta_correta": 0
            },
            {
                "pergunta": "Qual prática tem o objetivo de oferecer suporte à qualidade do serviço, manipulando todos os serviços acordados e iniciados pelo usuário?",
                "alternativas": ["Habilitação de mudanças", "Gerenciamento de ativos de TI", "Service desk", "Gerenciamento de Requisição de serviço"],
                "resposta_correta": 3
            },
            {
                "pergunta": "Qual NÃO é um componente do sistema de valor de serviço - SVS?",
                "alternativas": ["Os princípios orientadores", "Governança", "Práticas", "As quatro dimensões do gerenciamento de serviços"],
                "resposta_correta": 3
            },
            {
                "pergunta": "Qual afirmação sobre as etapas para atender a uma requisição de serviço está CORRETA?",
                "alternativas": ["Elas devem ser complexas e detalhadas", "Elas devem ser bem conhecidas e comprovadas", "Elas devem incluir tratamento de incidentes", "Elas devem ser breves e simples"],
                "resposta_correta": 1
            },
            {
                "pergunta": "Qual das seguintes opções é definido como causa real ou potencial de um ou mais incidentes?",
                "alternativas": ["Mudança", "Evento", "Erro conhecido", "Problema"],
                "resposta_correta": 3
            },
            {
                "pergunta": "Qual princípio orientador recomenda a eliminação de atividades que não contribuem para a criação de valor?",
                "alternativas": ["Comece de onde você está", "Colaborar e promover visibilidade", "Manter de forma simples e prática", "Otimizar e automatizar"],
                "resposta_correta": 2
            },
            {
                "pergunta": "Quando a eficácia de uma solução alternativa do problema deve ser avaliada?",
                "alternativas": ["Sempre que a solução alternativa for usada", "Sempre que o problema for resolvido", "Sempre que a solução alternativa se tornar um erro conhecido", "Sempre que o problema é priorizado"],
                "resposta_correta": 0
            },
            {
                "pergunta": "Identifique a palavra que está faltando na frase a seguir. Uma mudança é definida como uma adição, modificação ou remoção de qualquer coisa que possa ter um efeito direto ou indireto sobre [?].",
                "alternativas": ["ativos", "valores", "elementos", "serviços"],
                "resposta_correta": 3
            }
        ]
        
        processed_questions = []
        
        for i, q_data in enumerate(daypo_questions_raw):
            category, subcategory = self.categorize_question(q_data["pergunta"])
            
            question = {
                'id': self.question_id_counter + i,
                'pergunta': self.clean_text(q_data["pergunta"]),
                'alternativas': [self.clean_text(alt) for alt in q_data["alternativas"]],
                'resposta_correta': q_data["resposta_correta"],
                'categoria': category,
                'subcategoria': subcategory,
                'dificuldade': self.determine_difficulty(q_data["pergunta"]),
                'explicacao': f"Questão extraída do Daypo ITIL 4 TODAS 07-2023 sobre {category.replace('_', ' ')}",
                'simulados': ['ITIL_4_TODAS_07_2023'],
                'idioma': 'pt-br',
                'fonte': 'Daypo'
            }
            
            processed_questions.append(question)
        
        return processed_questions

    def add_more_daypo_questions(self) -> List[Dict]:
        """Adiciona mais questões baseadas no padrão do Daypo."""
        additional_questions = [
            {
                "pergunta": "Qual é o propósito da prática de 'gerenciamento de incidente'?",
                "alternativas": [
                    "Minimizar o impacto negativo de incidentes restaurando a operação normal de serviço o mais rápido possível",
                    "Capturar a demanda de resolução de incidentes e requisições de serviços",
                    "Reduzir a probabilidade e o impacto de incidentes por meio da identificação de suas causas reais e potenciais",
                    "Suportar a qualidade do serviço acordado através do tratamento eficaz de todas as requisições de serviços acordadas e iniciadas pelos usuários"
                ],
                "resposta_correta": 0
            },
            {
                "pergunta": "O que é definido como uma interrupção não planejada ou redução na qualidade de um serviço?",
                "alternativas": ["Um incidente", "Um problema", "Uma mudança", "Um evento"],
                "resposta_correta": 0
            },
            {
                "pergunta": "Qual prática ITIL tem o propósito de estabelecer e nutrir os vínculos entre a organização e suas partes interessadas em níveis estratégicos e táticos?",
                "alternativas": ["Gerenciamento de fornecedor", "Controle de mudanças", "Gerenciamento de relacionamento", "Central de serviço"],
                "resposta_correta": 2
            },
            {
                "pergunta": "O que pode ajudar a reduzir a resistência a uma melhoria planejada ao aplicar o princípio orientador 'colaborar e promover visibilidade'?",
                "alternativas": [
                    "Restringir informações sobre a melhoria apenas às partes interessadas essenciais",
                    "Aumentar a colaboração e visibilidade para a melhoria",
                    "Envolver clientes após todo o planejamento ter sido concluído",
                    "Envolver cada grupo de partes interessadas da mesma forma, com a mesma comunicação"
                ],
                "resposta_correta": 1
            },
            {
                "pergunta": "O que varia em tamanho e complexidade e usa funções para alcançar seus objetivos?",
                "alternativas": ["Um risco", "Uma organização", "Uma prática", "Um resultado"],
                "resposta_correta": 2
            }
        ]
        
        processed_questions = []
        start_id = self.question_id_counter + 100
        
        for i, q_data in enumerate(additional_questions):
            category, subcategory = self.categorize_question(q_data["pergunta"])
            
            question = {
                'id': start_id + i,
                'pergunta': self.clean_text(q_data["pergunta"]),
                'alternativas': [self.clean_text(alt) for alt in q_data["alternativas"]],
                'resposta_correta': q_data["resposta_correta"],
                'categoria': category,
                'subcategoria': subcategory,
                'dificuldade': self.determine_difficulty(q_data["pergunta"]),
                'explicacao': f"Questão adicional do Daypo sobre {category.replace('_', ' ')}",
                'simulados': ['ITIL_4_TODAS_07_2023'],
                'idioma': 'pt-br',
                'fonte': 'Daypo'
            }
            
            processed_questions.append(question)
        
        return processed_questions

    def save_daypo_questions(self, output_path: str):
        """Salva questões do Daypo em arquivo JSON."""
        # Extrai questões principais
        main_questions = self.extract_questions_from_daypo_content()
        
        # Adiciona questões adicionais
        additional_questions = self.add_more_daypo_questions()
        
        all_questions = main_questions + additional_questions
        
        # Carrega questões existentes
        existing_questions = []
        if os.path.exists(output_path):
            try:
                with open(output_path, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                existing_questions = existing_data.get('questoes', [])
            except Exception as e:
                logger.error(f"Erro ao carregar questões existentes: {e}")
        
        # Combina questões (evita duplicatas por ID)
        existing_ids = {q['id'] for q in existing_questions}
        new_questions = [q for q in all_questions if q['id'] not in existing_ids]
        
        # Atualiza dados
        all_combined_questions = existing_questions + new_questions
        
        # Carrega estrutura existente ou cria nova
        if os.path.exists(output_path):
            with open(output_path, 'r', encoding='utf-8') as f:
                database = json.load(f)
        else:
            database = {'questoes': [], 'categorias': [], 'simulados': []}
        
        database['questoes'] = all_combined_questions
        
        # Atualiza simulados se necessário
        daypo_simulado = {
            'id': 'ITIL_4_TODAS_07_2023',
            'nome': 'ITIL 4 TODAS 07-2023',
            'total_questoes': len([q for q in all_combined_questions if 'ITIL_4_TODAS_07_2023' in q.get('simulados', [])]),
            'descricao': 'Compilação completa de questões ITIL 4 extraídas do Daypo'
        }
        
        # Atualiza ou adiciona simulado
        simulado_exists = False
        for i, sim in enumerate(database['simulados']):
            if sim['id'] == 'ITIL_4_TODAS_07_2023':
                database['simulados'][i] = daypo_simulado
                simulado_exists = True
                break
        
        if not simulado_exists:
            database['simulados'].append(daypo_simulado)
        
        # Salva arquivo
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(database, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Questões do Daypo salvas: {output_path}")
        logger.info(f"Questões adicionadas: {len(new_questions)}")
        logger.info(f"Total de questões no banco: {len(all_combined_questions)}")

def main():
    """Função principal."""
    extractor = DaypoQuestionExtractor()
    
    # Caminho do arquivo de questões
    output_path = os.path.join(os.path.dirname(__file__), 'data', 'questions.json')
    
    # Extrai e salva questões do Daypo
    extractor.save_daypo_questions(output_path)

if __name__ == '__main__':
    main()
