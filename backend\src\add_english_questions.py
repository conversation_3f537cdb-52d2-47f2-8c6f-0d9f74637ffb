#!/usr/bin/env python3
"""
Script para adicionar questões ITIL 4 em inglês ao banco de dados.
"""

import json
import os
from typing import List, Dict

def add_english_questions():
    """Adiciona questões em inglês ao banco de dados."""
    
    # Questões ITIL 4 em inglês
    english_questions = [
        {
            "id": 3000,
            "pergunta": "What is the purpose of the 'service desk' practice?",
            "alternativas": [
                "To capture demand for incident resolution and service requests",
                "To reduce the likelihood and impact of incidents by identifying actual and potential causes",
                "To maximize the number of successful IT changes by ensuring risks are properly assessed",
                "To set clear business-based targets for service levels"
            ],
            "resposta_correta": 0,
            "categoria": "praticas_gerenciamento",
            "subcategoria": "central_servico",
            "dificuldade": "facil",
            "explicacao": "The service desk practice serves as the single point of contact between the service provider and users. Its purpose is to capture demand for incident resolution and service requests.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en"
        },
        {
            "id": 3001,
            "pergunta": "Which guiding principle recommends using existing services, processes, and tools when improving services?",
            "alternativas": [
                "Progress iteratively with feedback",
                "Keep it simple and practical", 
                "Start where you are",
                "Focus on value"
            ],
            "resposta_correta": 2,
            "categoria": "principios_orientadores",
            "subcategoria": "comecar_onde_esta",
            "dificuldade": "facil",
            "explicacao": "The 'Start where you are' guiding principle recommends assessing the current state and building on what already exists rather than starting from scratch.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en"
        },
        {
            "id": 3002,
            "pergunta": "What is defined as 'any component that needs to be managed in order to deliver an IT service'?",
            "alternativas": [
                "An event",
                "An IT asset",
                "A configuration item",
                "A change"
            ],
            "resposta_correta": 2,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "definicoes_basicas",
            "dificuldade": "facil",
            "explicacao": "A configuration item (CI) is defined as any component that needs to be managed in order to deliver an IT service. CIs are managed through the service configuration management practice.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en"
        },
        {
            "id": 3003,
            "pergunta": "Which practice has a purpose that includes ensuring that risks are properly assessed?",
            "alternativas": [
                "Service configuration management",
                "Problem management",
                "Service level management",
                "Change enablement"
            ],
            "resposta_correta": 3,
            "categoria": "praticas_gerenciamento",
            "subcategoria": "habilitacao_mudanca",
            "dificuldade": "facil",
            "explicacao": "Change enablement practice has a purpose that includes ensuring that risks are properly assessed, authorizing changes to proceed, and managing a change schedule.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en"
        },
        {
            "id": 3004,
            "pergunta": "What is warranty?",
            "alternativas": [
                "Assurance that a product or service will meet agreed requirements",
                "The amount of money spent on a specific activity or resource",
                "The functionality offered by a product or service to meet a particular need",
                "The perceived benefits, usefulness and importance of something"
            ],
            "resposta_correta": 0,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "definicoes_basicas",
            "dificuldade": "facil",
            "explicacao": "Warranty is the assurance that a product or service will meet agreed requirements. It relates to how the service performs - availability, capacity, security, and continuity.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en"
        },
        {
            "id": 3005,
            "pergunta": "Which dimension of service management considers how knowledge assets should be protected?",
            "alternativas": [
                "Organizations and people",
                "Partners and suppliers",
                "Information and technology",
                "Value streams and processes"
            ],
            "resposta_correta": 2,
            "categoria": "quatro_dimensoes",
            "subcategoria": "informacao_tecnologia",
            "dificuldade": "facil",
            "explicacao": "The 'Information and technology' dimension includes information security considerations and how knowledge assets should be protected, stored, and managed.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en"
        },
        {
            "id": 3006,
            "pergunta": "What is the purpose of the 'problem management' practice?",
            "alternativas": [
                "To capture demand for incident resolution and service requests",
                "To reduce the likelihood and impact of incidents by identifying actual and potential causes",
                "To restore normal service operation as quickly as possible",
                "To ensure that services achieve agreed and expected performance"
            ],
            "resposta_correta": 1,
            "categoria": "praticas_gerenciamento",
            "subcategoria": "gerenciamento_problema",
            "dificuldade": "facil",
            "explicacao": "Problem management aims to reduce the likelihood and impact of incidents by identifying their actual and potential causes and managing workarounds and known errors.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en"
        },
        {
            "id": 3007,
            "pergunta": "Which guiding principle recommends eliminating activities that do not contribute to value creation?",
            "alternativas": [
                "Start where you are",
                "Collaborate and promote visibility",
                "Keep it simple and practical",
                "Optimize and automate"
            ],
            "resposta_correta": 2,
            "categoria": "principios_orientadores",
            "subcategoria": "manter_simples_pratico",
            "dificuldade": "facil",
            "explicacao": "The 'Keep it simple and practical' guiding principle recommends eliminating activities that do not contribute to value creation and using the minimum number of steps to accomplish objectives.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en"
        },
        {
            "id": 3008,
            "pergunta": "What is defined as an unplanned interruption or reduction in quality of a service?",
            "alternativas": [
                "An incident",
                "A problem", 
                "A change",
                "An event"
            ],
            "resposta_correta": 0,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "definicoes_basicas",
            "dificuldade": "facil",
            "explicacao": "An incident is defined as an unplanned interruption to a service or reduction in the quality of a service. Incidents are managed through the incident management practice.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en"
        },
        {
            "id": 3009,
            "pergunta": "Which practice is responsible for moving new or changed components to live environments?",
            "alternativas": [
                "Change enablement",
                "Release management",
                "IT asset management",
                "Deployment management"
            ],
            "resposta_correta": 3,
            "categoria": "praticas_gerenciamento",
            "subcategoria": "gerenciamento_implantacao",
            "dificuldade": "facil",
            "explicacao": "Deployment management is responsible for moving new or changed hardware, software, documentation, processes, or any other component to live environments.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en"
        }
    ]
    
    # Carrega banco de dados existente
    questions_file = os.path.join(os.path.dirname(__file__), 'data', 'questions.json')
    
    try:
        with open(questions_file, 'r', encoding='utf-8') as f:
            database = json.load(f)
    except FileNotFoundError:
        database = {'questoes': [], 'categorias': [], 'simulados': []}
    
    # Adiciona questões em inglês (evita duplicatas)
    existing_ids = {q['id'] for q in database['questoes']}
    new_questions = [q for q in english_questions if q['id'] not in existing_ids]
    
    database['questoes'].extend(new_questions)
    
    # Adiciona simulado em inglês se não existir
    english_simulado = {
        'id': 'ITIL_4_ENGLISH_COLLECTION',
        'nome': 'ITIL 4 English Collection',
        'total_questoes': len([q for q in database['questoes'] if q.get('idioma') == 'en']),
        'descricao': 'ITIL 4 questions in English for international certification preparation'
    }
    
    # Verifica se simulado já existe
    simulado_exists = False
    for i, sim in enumerate(database['simulados']):
        if sim['id'] == 'ITIL_4_ENGLISH_COLLECTION':
            database['simulados'][i] = english_simulado
            simulado_exists = True
            break
    
    if not simulado_exists:
        database['simulados'].append(english_simulado)
    
    # Salva banco de dados atualizado
    with open(questions_file, 'w', encoding='utf-8') as f:
        json.dump(database, f, ensure_ascii=False, indent=2)
    
    print(f"Adicionadas {len(new_questions)} questões em inglês")
    print(f"Total de questões no banco: {len(database['questoes'])}")
    
    # Mostra estatísticas por idioma
    idiomas = {}
    for q in database['questoes']:
        lang = q.get('idioma', 'pt-br')
        idiomas[lang] = idiomas.get(lang, 0) + 1
    
    print("\nEstatísticas por idioma:")
    for lang, count in idiomas.items():
        lang_name = {'pt-br': 'Português', 'en': 'English', 'es': 'Español'}.get(lang, lang)
        print(f"  {lang_name}: {count} questões")

if __name__ == '__main__':
    add_english_questions()
