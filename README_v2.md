# ITILPrep Pro v2.0 🚀

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/React-18.3+-blue.svg)](https://reactjs.org/)
[![Flask](https://img.shields.io/badge/Flask-3.1+-red.svg)](https://flask.palletsprojects.com/)

Uma plataforma completa e profissional para preparação para certificação ITIL 4, desenvolvida com React + Vite no frontend e Flask no backend.

## ✨ Novidades da Versão 2.0

### 🌍 **Suporte Multi-idioma**
- **Português (Brasil)**: 147+ questões
- **English**: 10+ questões  
- Simulados específicos por idioma
- Interface adaptável

### 🎯 **Navegação Aprimorada**
- **Botão "Anterior"** para voltar às questões
- **Botão "Pular"** para avançar sem responder
- **Navegação livre** entre questões
- **Interface intuitiva** com ícones

### 📚 **Base de Conhecimento Expandida**
- **147+ questões** no total (aumento de 1370% em relação à versão 1.0)
- **Múltiplas fontes**: Daypo, ITIL-DUMP.txt, questões originais
- **Categorização inteligente** por tópicos ITIL 4
- **Explicações detalhadas** para cada questão

### 🏗️ **Estrutura Profissional**
- **Header** com navegação principal
- **Sidebar** com espaços para Google AdSense
- **Footer** com links e informações
- **Breadcrumb** para orientação
- **Design responsivo** para todos os dispositivos

### 💰 **Monetização Integrada**
- **Espaços para Google AdSense** pré-configurados
- **Guia completo** de configuração do AdSense
- **Posicionamento estratégico** dos anúncios
- **Performance otimizada**

## 🎯 Funcionalidades Principais

### 📖 **Base de Conhecimento ITIL 4**
- **Conceitos Fundamentais**: Sistema de Valor de Serviço, Definições, Quatro Dimensões
- **Princípios Orientadores**: Todos os 7 princípios com explicações detalhadas
- **Práticas de Gerenciamento**: Central de Serviços, Incidentes, Problemas, Mudanças
- **Cadeia de Valor**: 6 atividades principais
- **Melhoria Contínua**: Modelo e ferramentas
- **Busca integrada** por conceitos

### 🎮 **Modos de Simulado**
- **Simulado Completo**: 40 questões, 60 minutos, experiência real do exame
- **Modo Rápido**: 5 questões com feedback imediato
- **Por Categoria**: Foque em áreas específicas
- **Por Idioma**: Português ou Inglês
- **Simulados Originais**: Baseados em fontes confiáveis

### 💡 **Dicas de Estudo**
- **Estratégias comprovadas** para aprovação
- **Cronograma de 8 semanas** de estudos
- **Técnicas por estilo de aprendizagem**
- **Gestão de tempo** e preparação para o exame
- **Estatísticas do exame** ITIL 4 Foundation

### 📋 **Fontes e Atribuições**
- **Transparência total** sobre fontes de conteúdo
- **Links para recursos oficiais** ITIL 4
- **Compliance com direitos autorais**
- **Uso justo educacional**

## 🚀 Tecnologias

### Frontend
- **React 18** com TypeScript
- **Vite** para build e desenvolvimento rápido
- **Tailwind CSS** para estilização moderna
- **Shadcn/ui** para componentes profissionais
- **Lucide React** para ícones
- **React Query** para gerenciamento de estado

### Backend
- **Flask** (Python) com estrutura profissional
- **CORS** habilitado para desenvolvimento
- **JSON** otimizado para armazenamento
- **Logging** detalhado
- **APIs RESTful** bem documentadas

## 📦 Instalação e Configuração

### Pré-requisitos
- **Node.js 18+** 
- **Python 3.8+**
- **npm** ou **yarn**
- **Git** para controle de versão

### 🔧 Setup Completo

#### 1. Clone o Repositório
```bash
git clone https://github.com/hiagodrigo/ItilPrepPro.git
cd ItilPrepPro
```

#### 2. Backend Setup
```bash
cd backend

# Criar ambiente virtual
python -m venv venv

# Ativar ambiente virtual
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Instalar dependências
pip install -r requirements.txt

# Executar servidor
cd src
python main.py
```

#### 3. Frontend Setup
```bash
cd frontend

# Instalar dependências
npm install

# Executar servidor de desenvolvimento
npm run dev
```

### 🌐 URLs de Acesso
- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:5000
- **API Health Check**: http://localhost:5000/api/health

## 💰 Configuração do Google AdSense

### 📋 Guia Completo
Consulte o arquivo `GOOGLE_ADSENSE_SETUP.md` para instruções detalhadas sobre:

1. **Criação da conta** Google AdSense
2. **Configuração de unidades** de anúncio
3. **Implementação no código**
4. **Otimização de performance**
5. **Monitoramento de resultados**

### 🎯 Espaços de Anúncio Pré-configurados
- **Sidebar Top**: 300x250 Medium Rectangle
- **Sidebar Middle**: 300x600 Half Page
- **Sidebar Bottom**: 300x250 Responsive

## 📊 Estatísticas da Aplicação

| Métrica | Versão 1.0 | Versão 2.0 | Melhoria |
|---------|-------------|------------|----------|
| **Questões** | 10 | 147+ | +1370% |
| **Idiomas** | 1 | 2 | +100% |
| **Seções** | 1 | 5 | +400% |
| **Fontes** | 1 | 4+ | +300% |
| **Navegação** | Linear | Livre | ✅ |
| **Monetização** | ❌ | ✅ AdSense | ✅ |

## 🔗 API Endpoints

### 📋 Questões
- `GET /api/questions` - Todas as questões, categorias e simulados
- `GET /api/questions/random/{count}?lang={idioma}` - Questões aleatórias
- `GET /api/questions/category/{category}` - Questões por categoria
- `GET /api/questions/simulado/{simulado}` - Questões por simulado

### 🌍 Idiomas e Metadados
- `GET /api/languages` - Idiomas disponíveis com contadores
- `GET /api/categories` - Todas as categorias
- `GET /api/simulados` - Todos os simulados
- `GET /api/health` - Status da API

### 📝 Exemplo de Resposta
```json
{
  "questoes": [...],
  "categorias": [
    {
      "id": "conceitos_fundamentais",
      "nome": "Conceitos Fundamentais",
      "descricao": "Base conceitual do ITIL 4",
      "subcategorias": ["sistema_valor_servico", "quatro_dimensoes"]
    }
  ],
  "simulados": [...]
}
```

## 🧪 Testes e Qualidade

### Backend
```bash
cd backend
source venv/Scripts/activate
pytest -v
```

### Frontend
```bash
cd frontend
npm test
npm run test:coverage
```

### Linting e Formatação
```bash
# Frontend
npm run lint
npm run format

# Backend
flake8 src/
black src/
```

## 🔧 Configuração Avançada

### Variáveis de Ambiente

#### Backend (.env)
```env
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=False
CORS_ORIGINS=http://localhost:3001
```

#### Frontend (.env)
```env
VITE_API_URL=http://localhost:5000/api
VITE_ADSENSE_CLIENT_ID=ca-pub-XXXXXXXXXXXXXXXX
VITE_ADSENSE_SLOT_SIDEBAR_TOP=YYYYYYYYYY
VITE_ADSENSE_SLOT_SIDEBAR_MIDDLE=ZZZZZZZZZZ
VITE_ADSENSE_SLOT_SIDEBAR_BOTTOM=WWWWWWWWWW
```

## 📈 Performance e SEO

### Otimizações Implementadas
- ✅ **Lazy loading** de componentes
- ✅ **Code splitting** automático
- ✅ **Compressão** de assets
- ✅ **Cache** otimizado
- ✅ **Meta tags** SEO
- ✅ **Sitemap** automático
- ✅ **Analytics** ready

### Core Web Vitals
- **LCP**: < 2.5s
- **FID**: < 100ms
- **CLS**: < 0.1

## 🤝 Contribuição

### Como Contribuir
1. **Fork** o projeto
2. **Clone** seu fork
3. **Crie** uma branch (`git checkout -b feature/nova-funcionalidade`)
4. **Commit** suas mudanças (`git commit -m 'Adiciona nova funcionalidade'`)
5. **Push** para a branch (`git push origin feature/nova-funcionalidade`)
6. **Abra** um Pull Request

### Diretrizes
- ✅ Siga os padrões de código existentes
- ✅ Adicione testes para novas funcionalidades
- ✅ Atualize a documentação
- ✅ Use commits semânticos

## 📄 Licença e Atribuições

### Licença
Este projeto está sob a **licença MIT**. Veja [LICENSE](LICENSE) para detalhes.

### Atribuições de Conteúdo
- **ITIL®** é marca registrada da AXELOS Limited
- **Questões Daypo**: Uso educacional sob Fair Use
- **Conteúdo original**: ITILPrep Pro Team
- **Fontes completas**: Veja seção "Fontes e Referências" na aplicação

### Disclaimer
Este site não é afiliado à AXELOS Limited. Todo conteúdo é usado para fins educacionais sob os princípios de Uso Justo.

## 🙏 Agradecimentos

- **Comunidade ITIL 4** pelo conhecimento compartilhado
- **Daypo** pelas questões de prática
- **AXELOS** pelo framework ITIL 4
- **Contribuidores** do projeto
- **Usuários** que ajudam a melhorar a plataforma

## 📞 Suporte e Contato

- **GitHub Issues**: Para bugs e sugestões
- **Email**: <EMAIL>
- **Documentação**: Veja arquivos `.md` no repositório

---

**ITILPrep Pro v2.0** - Sua plataforma completa para conquistar a certificação ITIL 4! 🎯✨

*Transformando conhecimento em certificação desde 2024* 🚀
