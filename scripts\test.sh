#!/bin/bash

# Script para executar todos os testes do projeto Simulado ITIL 4
# Este script executa testes do frontend e backend com relatórios de cobertura

set -e  # Sair em caso de erro

echo "🧪 Executando testes do projeto Simulado ITIL 4..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir mensagens coloridas
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Variáveis para controlar execução
RUN_FRONTEND=true
RUN_BACKEND=true
COVERAGE=false
WATCH=false

# Processar argumentos da linha de comando
while [[ $# -gt 0 ]]; do
    case $1 in
        --frontend-only)
            RUN_BACKEND=false
            shift
            ;;
        --backend-only)
            RUN_FRONTEND=false
            shift
            ;;
        --coverage)
            COVERAGE=true
            shift
            ;;
        --watch)
            WATCH=true
            shift
            ;;
        --help)
            echo "Uso: $0 [opções]"
            echo "Opções:"
            echo "  --frontend-only    Executar apenas testes do frontend"
            echo "  --backend-only     Executar apenas testes do backend"
            echo "  --coverage         Gerar relatórios de cobertura"
            echo "  --watch           Executar testes em modo watch (apenas frontend)"
            echo "  --help            Mostrar esta ajuda"
            exit 0
            ;;
        *)
            print_error "Opção desconhecida: $1"
            echo "Use --help para ver as opções disponíveis"
            exit 1
            ;;
    esac
done

# Executar testes do frontend
test_frontend() {
    print_status "Executando testes do frontend..."
    
    cd frontend
    
    # Verificar se node_modules existe
    if [ ! -d "node_modules" ]; then
        print_warning "node_modules não encontrado. Instalando dependências..."
        if command -v pnpm &> /dev/null; then
            pnpm install
        elif command -v yarn &> /dev/null; then
            yarn install
        else
            npm install
        fi
    fi
    
    # Preparar comando de teste
    if command -v pnpm &> /dev/null; then
        TEST_CMD="pnpm run test"
    elif command -v yarn &> /dev/null; then
        TEST_CMD="yarn test"
    else
        TEST_CMD="npm run test"
    fi
    
    # Adicionar opções baseadas nos parâmetros
    if [ "$WATCH" = true ]; then
        TEST_CMD="$TEST_CMD --watch"
    else
        TEST_CMD="$TEST_CMD --run"
    fi
    
    if [ "$COVERAGE" = true ]; then
        TEST_CMD="$TEST_CMD --coverage"
    fi
    
    # Executar testes
    print_status "Comando: $TEST_CMD"
    eval $TEST_CMD
    
    if [ $? -eq 0 ]; then
        print_success "Testes do frontend executados com sucesso!"
    else
        print_error "Falha nos testes do frontend"
        return 1
    fi
    
    cd ..
}

# Executar testes do backend
test_backend() {
    print_status "Executando testes do backend..."
    
    cd backend
    
    # Verificar se ambiente virtual existe
    if [ ! -d "venv" ]; then
        print_warning "Ambiente virtual não encontrado. Criando..."
        python3 -m venv venv 2>/dev/null || python -m venv venv
    fi
    
    # Ativar ambiente virtual
    source venv/bin/activate
    
    # Verificar se pytest está instalado
    if ! command -v pytest &> /dev/null; then
        print_warning "pytest não encontrado. Instalando dependências de desenvolvimento..."
        if [ -f "requirements-dev.txt" ]; then
            pip install -r requirements-dev.txt
        else
            pip install pytest pytest-cov
        fi
    fi
    
    # Preparar comando de teste
    TEST_CMD="pytest -v"
    
    if [ "$COVERAGE" = true ]; then
        TEST_CMD="$TEST_CMD --cov=src --cov-report=html --cov-report=term"
    fi
    
    # Executar testes
    print_status "Comando: $TEST_CMD"
    eval $TEST_CMD
    
    if [ $? -eq 0 ]; then
        print_success "Testes do backend executados com sucesso!"
        
        if [ "$COVERAGE" = true ] && [ -d "htmlcov" ]; then
            print_status "Relatório de cobertura gerado em: backend/htmlcov/index.html"
        fi
    else
        print_error "Falha nos testes do backend"
        return 1
    fi
    
    cd ..
}

# Executar linting
run_linting() {
    print_status "Executando verificações de qualidade de código..."
    
    # Linting do frontend
    if [ "$RUN_FRONTEND" = true ]; then
        print_status "Verificando código do frontend..."
        cd frontend
        
        if command -v pnpm &> /dev/null; then
            pnpm run lint
        elif command -v yarn &> /dev/null; then
            yarn lint
        else
            npm run lint
        fi
        
        cd ..
    fi
    
    # Linting do backend
    if [ "$RUN_BACKEND" = true ]; then
        print_status "Verificando código do backend..."
        cd backend
        
        source venv/bin/activate
        
        # Verificar se as ferramentas estão instaladas
        if command -v black &> /dev/null; then
            print_status "Verificando formatação com black..."
            black --check src/
        fi
        
        if command -v flake8 &> /dev/null; then
            print_status "Verificando estilo com flake8..."
            flake8 src/
        fi
        
        if command -v mypy &> /dev/null; then
            print_status "Verificando tipos com mypy..."
            mypy src/ || print_warning "Verificação de tipos falhou (não crítico)"
        fi
        
        cd ..
    fi
}

# Gerar relatório de resumo
generate_summary() {
    print_status "Gerando resumo dos testes..."
    
    echo ""
    echo "📊 RESUMO DOS TESTES"
    echo "===================="
    
    if [ "$RUN_FRONTEND" = true ]; then
        echo "✅ Frontend: Testes executados"
        if [ "$COVERAGE" = true ] && [ -f "frontend/coverage/index.html" ]; then
            echo "   📈 Cobertura: frontend/coverage/index.html"
        fi
    fi
    
    if [ "$RUN_BACKEND" = true ]; then
        echo "✅ Backend: Testes executados"
        if [ "$COVERAGE" = true ] && [ -f "backend/htmlcov/index.html" ]; then
            echo "   📈 Cobertura: backend/htmlcov/index.html"
        fi
    fi
    
    echo ""
    print_success "Todos os testes foram executados com sucesso! 🎉"
}

# Função principal
main() {
    print_status "Iniciando execução de testes..."
    
    # Verificar se estamos no diretório raiz do projeto
    if [ ! -f "README.md" ] || [ ! -d "frontend" ] || [ ! -d "backend" ]; then
        print_error "Execute este script a partir do diretório raiz do projeto"
        exit 1
    fi
    
    # Executar linting primeiro
    run_linting
    
    # Executar testes
    if [ "$RUN_FRONTEND" = true ]; then
        test_frontend
    fi
    
    if [ "$RUN_BACKEND" = true ]; then
        test_backend
    fi
    
    # Gerar resumo (apenas se não estiver em modo watch)
    if [ "$WATCH" = false ]; then
        generate_summary
    fi
}

# Executar função principal
main "$@"
