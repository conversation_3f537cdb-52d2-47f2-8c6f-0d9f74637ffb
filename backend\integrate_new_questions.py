#!/usr/bin/env python3
"""
Script para integrar novas questões ao banco de dados principal
"""

import json
import os

def load_json_file(filename):
    """Carrega arquivo JSON"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Arquivo {filename} não encontrado")
        return []
    except json.JSONDecodeError as e:
        print(f"Erro ao decodificar JSON em {filename}: {e}")
        return []

def save_json_file(data, filename):
    """Salva dados em arquivo JSON"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def integrate_questions():
    """Integra novas questões ao banco principal"""
    
    # Carregar questões existentes
    existing_questions = load_json_file('questoes.json')
    print(f"Questões existentes: {len(existing_questions)}")
    
    # Carregar questões adicionais
    additional_questions = load_json_file('questoes_adicionais.json')
    print(f"Questões adicionais: {len(additional_questions)}")
    
    # Carregar questões completas
    complete_questions = load_json_file('questoes_itil4_completas.json')
    print(f"Questões completas: {len(complete_questions)}")
    
    # Combinar todas as questões
    all_questions = existing_questions.copy()
    
    # Verificar IDs existentes para evitar duplicatas
    existing_ids = {q.get('id') for q in existing_questions}
    
    # Adicionar questões adicionais
    for q in additional_questions:
        if q.get('id') not in existing_ids:
            all_questions.append(q)
            existing_ids.add(q.get('id'))
        else:
            print(f"ID duplicado ignorado: {q.get('id')}")
    
    # Adicionar questões completas
    for q in complete_questions:
        if q.get('id') not in existing_ids:
            all_questions.append(q)
            existing_ids.add(q.get('id'))
        else:
            print(f"ID duplicado ignorado: {q.get('id')}")
    
    print(f"Total de questões após integração: {len(all_questions)}")
    
    # Salvar questões integradas
    save_json_file(all_questions, 'questoes_integradas.json')
    print("Questões integradas salvas em: questoes_integradas.json")
    
    # Fazer backup do arquivo original
    if os.path.exists('questoes.json'):
        save_json_file(existing_questions, 'questoes_backup.json')
        print("Backup criado: questoes_backup.json")
    
    # Substituir arquivo principal
    save_json_file(all_questions, 'questoes.json')
    print("Arquivo principal atualizado: questoes.json")
    
    # Estatísticas finais
    categorias = {}
    idiomas = {}
    dificuldades = {}
    
    for q in all_questions:
        cat = q.get('categoria', 'unknown')
        idioma = q.get('idioma', 'unknown')
        dif = q.get('dificuldade', 'unknown')
        
        categorias[cat] = categorias.get(cat, 0) + 1
        idiomas[idioma] = idiomas.get(idioma, 0) + 1
        dificuldades[dif] = dificuldades.get(dif, 0) + 1
    
    print("\n=== ESTATÍSTICAS FINAIS ===")
    print(f"Total de questões: {len(all_questions)}")
    
    print("\nPor categoria:")
    for cat, count in sorted(categorias.items()):
        print(f"  {cat}: {count} questões")
    
    print("\nPor idioma:")
    for idioma, count in sorted(idiomas.items()):
        print(f"  {idioma}: {count} questões")
    
    print("\nPor dificuldade:")
    for dif, count in sorted(dificuldades.items()):
        print(f"  {dif}: {count} questões")

if __name__ == "__main__":
    integrate_questions()
