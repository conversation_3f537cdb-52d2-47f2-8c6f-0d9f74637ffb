#!/usr/bin/env python3
"""
Script de teste simples para verificar se o Flask está funcionando
"""

try:
    from flask import Flask, jsonify
    from flask_cors import CORS
    
    app = Flask(__name__)
    CORS(app)
    
    @app.route('/test')
    def test():
        return jsonify({"message": "ITILPrep Pro Backend está funcionando!", "status": "ok"})
    
    @app.route('/api/health')
    def health():
        return jsonify({"status": "healthy", "message": "API do ITILPrep Pro está funcionando"})
    
    if __name__ == '__main__':
        print("Iniciando servidor de teste...")
        print("Acesse: http://localhost:5000/test")
        print("Ou: http://localhost:5000/api/health")
        app.run(host='0.0.0.0', port=5000, debug=True)
        
except ImportError as e:
    print(f"Erro de importação: {e}")
    print("Certifique-se de que o ambiente virtual está ativo e as dependências estão instaladas.")
except Exception as e:
    print(f"Erro inesperado: {e}")
