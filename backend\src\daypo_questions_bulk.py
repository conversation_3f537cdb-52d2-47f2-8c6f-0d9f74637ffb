#!/usr/bin/env python3
"""
Arquivo com questões adicionais extraídas do Daypo ITIL 4 TODAS 07-2023.
Este arquivo contém uma coleção expandida de questões para atingir as 239 questões.
"""

import json
import os
from extract_daypo_questions import DaypoQuestionExtractor

def get_bulk_daypo_questions():
    """Retorna lista expandida de questões do Daypo."""
    return [
        {
            "pergunta": "Que dimensão considera o modo como os ativos de conhecimento devem ser protegidos?",
            "alternativas": ["Organização e pessoas", "Parceiros e fornecedores", "Informação e tecnologia", "Fluxo de valor e processos"],
            "resposta_correta": 2
        },
        {
            "pergunta": "Qual é um meio de permitir a cocriação de valor ao facilitar a obtenção dos resultados que os clientes desejam alcançar, sem que eles precisem gerenciar custos e riscos específicos?",
            "alternativas": ["Gerenciamento de serviços", "Melhoria contínua", "Um serviço", "Um ativo de TI"],
            "resposta_correta": 2
        },
        {
            "pergunta": "Identifique a palavra que está faltando na frase a seguir. O gerenciamento de incidentes de segurança da informação geralmente requer [?].",
            "alternativas": ["Escalada imediata", "Equipes especializadas", "Um processo separado", "Suporte de terceiros"],
            "resposta_correta": 2
        },
        {
            "pergunta": "Para que são utilizados os princípios orientados do ITIL?",
            "alternativas": ["Para ajudar uma organização na tomada de boas decisões", "Dirigir e controlar uma organização", "Identificar atividades que uma organização deve realizar para prestar um serviço valioso", "Garantir que o desempenho de uma organização atenda continuamente às expectativas das partes interessadas"],
            "resposta_correta": 0
        },
        {
            "pergunta": "Qual é a abordagem CORRETA para gerenciar uma grande iniciativa de melhoria como iterações menores?",
            "alternativas": ["Cada iteração deve ser projetada antes de iniciar a iniciativa e implementada sem feedback", "O feedback só deve ser levado em consideração quando uma iteração falha em atingir seu objetivo", "O feedback deve ser reduzido para grandes melhorias, pois é improvável que as circunstâncias mudem", "Cada iteração deve ser continuamente reavaliada com base no feedback"],
            "resposta_correta": 3
        },
        {
            "pergunta": "Qual o propósito da prática 'gerenciamento de implantação'?",
            "alternativas": ["Garantir que os serviços atinjam o desempenho esperado e acordado", "Disponibilizar serviços novos ou alterados para uso", "Implantar componentes novos ou modificados para ambientes de produção", "Definir metas claras e baseadas no desempenho dos serviços"],
            "resposta_correta": 2
        },
        {
            "pergunta": "O que é uma requisição de serviço?",
            "alternativas": ["Solicitar uma solução alternativa para um problema", "Solicitar informações sobre como criar um documento", "Solicitar um aprimoramento para um aplicativo", "Solicitar investigação de um serviço degradado"],
            "resposta_correta": 1
        },
        {
            "pergunta": "Identifique a palavra que está faltando na frase a seguir. O propósito da prática de 'gerenciamento de fornecedor' é garantir que os fornecedores da organização e o [?] deles sejam gerenciados adequadamente para oferecer suporte a produtos e serviços de qualidade contínua.",
            "alternativas": ["custos", "usuários", "valor", "desempenho"],
            "resposta_correta": 3
        },
        {
            "pergunta": "Qual das recomendações a seguir está relacionada ao princípio orientador 'Foco no Valor'?",
            "alternativas": ["Tornar o princípio 'Foco no Valor' uma responsabilidade da gerência", "Manter o foco no valor de projetos novos e significativos primeiro", "Manter o foco no valor do provedor de serviços primeiro", "Manter o foco no valor a cada etapa de melhoria"],
            "resposta_correta": 3
        },
        {
            "pergunta": "Qual o princípio orientador recomenda padronizar e otimizar tarefas manuais?",
            "alternativas": ["Otimizar e automatizar", "Colaborar e promover visibilidade", "Foco no valor", "Pensar e trabalhar holisticamente"],
            "resposta_correta": 0
        },
        {
            "pergunta": "Qual é o principal requisito para um acordo de nível de serviço (ANS) de sucesso?",
            "alternativas": ["Deve ser escrito em linguagem jurídica", "Deve ser escrito de modo simples e fácil de entender", "Deve basear-se na visão de serviço do provedor de serviço", "Deve estar relacionado a métricas operacionais simples"],
            "resposta_correta": 1
        },
        {
            "pergunta": "O que descreve um conjunto de etapas definidas para implementar melhorias?",
            "alternativas": ["A atividade de cadeia de valor 'melhorar'", "O 'registro de melhoria contínua'", "O 'modelo de melhoria contínua'", "A atividade da cadeia de valor de 'engajamento'"],
            "resposta_correta": 2
        },
        {
            "pergunta": "Ao planejar a 'melhoria contínua', qual abordagem para avaliar o estado atual de um serviço é CORRETA?",
            "alternativas": ["Uma organização deve sempre usar uma técnica para garantir que as métricas sejam consistentes", "Uma organização deve sempre usar uma análise de força, fraqueza, oportunidade e ameaça (FOFA)", "Uma organização deve sempre desenvolver competências em metodologias e técnicas que atendam as suas necessidades", "Uma organização deve sempre usar uma abordagem que combine as metodologias Lean, Agile e DevOps"],
            "resposta_correta": 2
        },
        {
            "pergunta": "Como um consumidor de serviço contribui para a redução de risco?",
            "alternativas": ["Pagando pelo serviço", "Gerenciando o hardware do servidor", "Ao comunicar restrições", "Gerenciando a disponibilidade da equipe"],
            "resposta_correta": 2
        },
        {
            "pergunta": "O que ajuda a diagnosticar e resolver um incidente simples?",
            "alternativas": ["escala rápida", "Formação de uma equipe temporária", "Uso de roteiros", "Priorização de problemas"],
            "resposta_correta": 2
        },
        {
            "pergunta": "Qual prática ITIL tem o propósito que inclui reduzir a probabilidade de incidentes?",
            "alternativas": ["Controle de mudanças", "Melhoria contínua", "Gerenciamento de problemas", "Central de Serviços"],
            "resposta_correta": 2
        },
        {
            "pergunta": "Quais são as melhores métricas de nível de serviço para medir a experiência do usuário?",
            "alternativas": ["Métricas baseadas em um único sistema", "Métricas de porcentagem de tempo de atividade de um serviço", "Métricas operacionais", "Métricas vinculadas a resultados definidos"],
            "resposta_correta": 3
        },
        {
            "pergunta": "Quais são as qualificações MAIS importantes para a equipe da central de serviço?",
            "alternativas": ["Qualificações de análise de incidentes", "Qualificações técnicas", "Qualificações de resolução de problemas", "Qualificações de gerenciamento de fornecedores"],
            "resposta_correta": 2
        },
        {
            "pergunta": "Quais das DUAS afirmações sobre a cultura de uma organização são CORRETAS? 1. É criado a partir de valores compartilhados, com base em como realiza seu trabalho 2. É determinado pelo tipo de tecnologia usada para apoiar serviços 3. Deve basear-se na cultura de potenciais fornecedores 4. Deve basear-se nos objetivos da organização.",
            "alternativas": ["1 e 2", "2 e 3", "3 e 4", "1 e 4"],
            "resposta_correta": 3
        },
        {
            "pergunta": "Quando uma requisição de mudança deve ser enviada para resolver um problema?",
            "alternativas": ["Assim que uma solução alternativa para o problema for identificada", "Assim que uma solução para o problema for identificada", "Assim que a análise da frequência e do impacto dos incidentes justificar a mudança", "Assim que a análise de custo, risco e benefícios justificar a mudança"],
            "resposta_correta": 3
        },
        {
            "pergunta": "Qual prática minimiza o impacto em operações de serviço normais por meio do gerenciamento de recursos em resposta a redução não planejadas na qualidade do serviço?",
            "alternativas": ["Gerenciamento de incidente", "Habilitação de mudança", "Gerenciamento de nível de serviço", "Melhoria contínua"],
            "resposta_correta": 0
        },
        {
            "pergunta": "Qual o MELHOR tipo de recurso para a investigação de incidentes complexos?",
            "alternativas": ["Sistemas de autoatendimento", "Equipe de suporte capacitada", "Instruções de trabalho detalhadas", "Pianos de recuperação de desastre"],
            "resposta_correta": 1
        },
        {
            "pergunta": "Quais decisões de gerenciamento de requisição de serviço exigem que politicas sejam estabelecidas?",
            "alternativas": ["Decidir como as degradações de serviço são resolvidas", "Decidir como lidar com requisições de serviço em que as etapas são desconhecidas", "Decidir quais requisições de serviços exigem aprovação", "Decidir quando soluções de contorno devem ser usadas"],
            "resposta_correta": 2
        },
        {
            "pergunta": "A equipe de uma organização de TI está muito ocupada, principalmente realizando tarefas que adicionam pouco ou nenhum valor à organização ou aos clientes. Qual o princípio orientador recomenda que o trabalho desnecessário seja eliminado?",
            "alternativas": ["Manter de forma simples e prática", "Pensar e trabalhar holisticamente", "Começar de onde você está", "Progredir interativamente com feedback"],
            "resposta_correta": 0
        },
        {
            "pergunta": "Qual prática faz uso de métodos de Lean, Agile e DevOps?",
            "alternativas": ["Central de serviço", "Melhoria contínua", "Gerenciamento de problema", "Gerenciamento de incidente"],
            "resposta_correta": 1
        },
        {
            "pergunta": "Qual das opções é um exemplo de requisição de serviço?",
            "alternativas": ["Uma requisição para a operação normal ser restaurada", "Uma requisição para implementar uma correção de segurança", "Uma requisição para acesso a um arquivo", "Uma requisição para investigar a causa de um incidente"],
            "resposta_correta": 2
        },
        {
            "pergunta": "Identifique a palavra faltante na frase a seguir. O propósito da prática de gerenciamento de configuração de serviço é garantir que informações precisas e confiáveis sobre a configuração dos(das) [?] e sobre os itens de configuração que suportam os serviços estejam disponíveis quando e onde forem necessárias.",
            "alternativas": ["organizações", "resultados", "relacionamentos", "serviços"],
            "resposta_correta": 3
        },
        {
            "pergunta": "Qual das opções inclui a configuração de componentes e atividades para promover resultado para as partes interessadas?",
            "alternativas": ["Gerenciamento de relacionamento de serviço", "Consumo de serviço", "O sistema de valor de serviço", "A prática de gerenciamento de liberação"],
            "resposta_correta": 2
        },
        {
            "pergunta": "Qual prática tem o propósito que inclui maximizar o número de acréscimos, modificações ou remoções bem-sucedidas de qualquer elemento que possa afetar um serviço?",
            "alternativas": ["Gerenciamento de requisição de serviço", "Gerenciamento de incidente", "Central de serviço", "Habilitação de mudança"],
            "resposta_correta": 3
        },
        {
            "pergunta": "Qual termo é usado para descrever a funcionalidade de um serviço?",
            "alternativas": ["Saída", "Resultado", "Utilidade", "Garantia"],
            "resposta_correta": 2
        },
        {
            "pergunta": "O propósito de qual prática inclui a criação de relacionamentos mais próximos e colaborativos?",
            "alternativas": ["Gerenciamento de fornecedor", "Gerenciamento de segurança da informação", "Gerenciamento de liberação", "Gerenciamento de configuração de serviço"],
            "resposta_correta": 0
        },
        {
            "pergunta": "Qual fase do gerenciamento de problemas inclui a avaliação normal da efetividade de soluções de contorno?",
            "alternativas": ["Identificação de problemas", "Controle de problemas", "Controle de erros", "Análise de problemas"],
            "resposta_correta": 2
        },
        {
            "pergunta": "Um usuário entra em contato com a central de serviço para perguntar como se cria um relatório. Qual prática tem MAIS PROBABILIDADE de contribuir para resolver essa questão?",
            "alternativas": ["Gerenciamento de incidente", "Gerenciamento de nível de serviço", "Gerenciamento de requisição de serviço", "Habilitação de mudança"],
            "resposta_correta": 2
        },
        {
            "pergunta": "O que é definido como 'qualquer componente que precisa ser gerenciado a fim de entregar um serviço de TI'?",
            "alternativas": ["Um evento", "Um ativo de TI", "Um item de configuração", "Uma mudança"],
            "resposta_correta": 2
        },
        {
            "pergunta": "Qual destas afirmativas sobre ofertas de serviço está CORRETA?",
            "alternativas": ["O mesmo produto pode ser usado como base para mais de uma oferta de serviço", "As ofertas de serviço incluem a transferência de bens do consumidor para o provedor", "As ofertas de serviço descrevem como os provedores e consumidores cooperam para cocriar valor", "Cada serviço deve ser descrito para os consumidores como uma oferta de serviço única"],
            "resposta_correta": 0
        },
        {
            "pergunta": "Identifique a(s) palavra(s) que falta(m) na frase a seguir. O propósito da prática de gerenciamento de problema é reduzir a probabilidade e o impacto de incidentes por meio da identificação de suas causas reais e potenciais e do gerenciamento de soluções de contorno e [?].",
            "alternativas": ["eventos", "mudanças", "ativos de TI", "erros conhecidos"],
            "resposta_correta": 3
        },
        {
            "pergunta": "Uma falha em um aplicativo pode causar a falha de um serviço. A equipe de TI está analisando ativamente o aplicativo para tentar entender o que está acontecendo. Qual é o nome correto desse tipo de falha?",
            "alternativas": ["Problema", "Incidente", "Evento", "Erro conhecido"],
            "resposta_correta": 0
        },
        {
            "pergunta": "Qual prática é MAIS associada ao uso da empatia para entender os usuários?",
            "alternativas": ["Central de serviço", "Melhoria contínua", "Gerenciamento de nível de serviço", "Habilitação de mudança"],
            "resposta_correta": 0
        },
        {
            "pergunta": "Qual papel seria O MAIS adequado para alguém com muita experiência em funções de TI e de negócio? O profissional também tem experiência em gerenciamento de relacionamentos com várias partes interessadas, incluindo fornecedores e gerentes de negócio.",
            "alternativas": ["Gerente de nível de serviço", "Agente da central de serviço", "Autoridade de mudança", "Analista de problemas"],
            "resposta_correta": 0
        },
        {
            "pergunta": "Qual atividade da cadeia de valor de serviço lida com a aquisição de novos produtos?",
            "alternativas": ["Engajar", "Obtenção/construção", "Planejar", "Melhorar"],
            "resposta_correta": 1
        },
        {
            "pergunta": "Qual princípio orientador ajuda a garantir que melhores informações estejam disponíveis para a tomada de decisão?",
            "alternativas": ["Manter de forma simples e prática", "Colaborar e promover visibilidade", "Otimizar e automatizar", "Pensar e trabalhar holisticamente"],
            "resposta_correta": 1
        }
    ]

def main():
    """Processa e adiciona questões em lote ao banco de dados."""
    extractor = DaypoQuestionExtractor()
    
    # Obtém questões em lote
    bulk_questions = get_bulk_daypo_questions()
    
    # Processa questões
    processed_questions = []
    start_id = 2000  # ID alto para evitar conflitos
    
    for i, q_data in enumerate(bulk_questions):
        category, subcategory = extractor.categorize_question(q_data["pergunta"])
        
        question = {
            'id': start_id + i,
            'pergunta': extractor.clean_text(q_data["pergunta"]),
            'alternativas': [extractor.clean_text(alt) for alt in q_data["alternativas"]],
            'resposta_correta': q_data["resposta_correta"],
            'categoria': category,
            'subcategoria': subcategory,
            'dificuldade': extractor.determine_difficulty(q_data["pergunta"]),
            'explicacao': f"Questão do Daypo ITIL 4 TODAS 07-2023 sobre {category.replace('_', ' ')}",
            'simulados': ['ITIL_4_TODAS_07_2023'],
            'idioma': 'pt-br',
            'fonte': 'Daypo Bulk'
        }
        
        processed_questions.append(question)
    
    # Carrega banco existente
    output_path = os.path.join(os.path.dirname(__file__), 'data', 'questions.json')
    
    if os.path.exists(output_path):
        with open(output_path, 'r', encoding='utf-8') as f:
            database = json.load(f)
    else:
        database = {'questoes': [], 'categorias': [], 'simulados': []}
    
    # Adiciona novas questões (evita duplicatas)
    existing_ids = {q['id'] for q in database['questoes']}
    new_questions = [q for q in processed_questions if q['id'] not in existing_ids]
    
    database['questoes'].extend(new_questions)
    
    # Atualiza contagem do simulado
    for simulado in database['simulados']:
        if simulado['id'] == 'ITIL_4_TODAS_07_2023':
            simulado['total_questoes'] = len([q for q in database['questoes'] if 'ITIL_4_TODAS_07_2023' in q.get('simulados', [])])
            break
    
    # Salva banco atualizado
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(database, f, ensure_ascii=False, indent=2)
    
    print(f"Adicionadas {len(new_questions)} questões em lote")
    print(f"Total de questões no banco: {len(database['questoes'])}")

if __name__ == '__main__':
    main()
