@echo off
REM Script de configuração inicial do projeto Simulado ITIL 4 para Windows
REM Este script automatiza a instalação de dependências e configuração inicial

setlocal enabledelayedexpansion

echo 🚀 Configurando o projeto Simulado ITIL 4...

REM Verificar se Node.js está instalado
echo [INFO] Verificando Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js não encontrado. Por favor, instale Node.js 16+ antes de continuar.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo [SUCCESS] Node.js encontrado: !NODE_VERSION!
)

REM Verificar se Python está instalado
echo [INFO] Verificando Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    python3 --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo [ERROR] Python não encontrado. Por favor, instale Python 3.8+ antes de continuar.
        pause
        exit /b 1
    ) else (
        for /f "tokens=*" %%i in ('python3 --version') do set PYTHON_VERSION=%%i
        echo [SUCCESS] Python encontrado: !PYTHON_VERSION!
        set PYTHON_CMD=python3
    )
) else (
    for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
    echo [SUCCESS] Python encontrado: !PYTHON_VERSION!
    set PYTHON_CMD=python
)

REM Configurar frontend
echo [INFO] Configurando frontend...
cd frontend

if not exist "package.json" (
    echo [ERROR] package.json não encontrado no diretório frontend
    pause
    exit /b 1
)

REM Verificar gerenciador de pacotes disponível
where pnpm >nul 2>&1
if %errorlevel% equ 0 (
    echo [INFO] Usando pnpm para instalar dependências...
    pnpm install
) else (
    where yarn >nul 2>&1
    if %errorlevel% equ 0 (
        echo [INFO] Usando yarn para instalar dependências...
        yarn install
    ) else (
        echo [INFO] Usando npm para instalar dependências...
        npm install
    )
)

if %errorlevel% neq 0 (
    echo [ERROR] Falha ao instalar dependências do frontend
    pause
    exit /b 1
)

echo [SUCCESS] Frontend configurado com sucesso!
cd ..

REM Configurar backend
echo [INFO] Configurando backend...
cd backend

REM Criar ambiente virtual se não existir
if not exist "venv" (
    echo [INFO] Criando ambiente virtual Python...
    %PYTHON_CMD% -m venv venv
    if %errorlevel% neq 0 (
        echo [ERROR] Falha ao criar ambiente virtual
        pause
        exit /b 1
    )
)

REM Ativar ambiente virtual
echo [INFO] Ativando ambiente virtual...
call venv\Scripts\activate.bat

REM Atualizar pip
echo [INFO] Atualizando pip...
python -m pip install --upgrade pip

REM Instalar dependências
echo [INFO] Instalando dependências Python...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [ERROR] Falha ao instalar dependências do backend
    pause
    exit /b 1
)

REM Instalar dependências de desenvolvimento se o arquivo existir
if exist "requirements-dev.txt" (
    echo [INFO] Instalando dependências de desenvolvimento...
    pip install -r requirements-dev.txt
)

REM Criar arquivo .env se não existir
if not exist ".env" (
    if exist ".env.example" (
        echo [INFO] Criando arquivo .env...
        copy .env.example .env
        echo [WARNING] Arquivo .env criado. Revise as configurações se necessário.
    )
)

echo [SUCCESS] Backend configurado com sucesso!
cd ..

echo [SUCCESS] 🎉 Projeto configurado com sucesso!
echo.
echo [INFO] Próximos passos:
echo 1. Para executar o frontend: cd frontend ^&^& npm run dev
echo 2. Para executar o backend: cd backend ^&^& venv\Scripts\activate ^&^& python src\main.py
echo 3. Para executar testes: npm run test (frontend) ou pytest (backend)
echo.
echo [INFO] Acesse http://localhost:3000 para ver a aplicação

pause
