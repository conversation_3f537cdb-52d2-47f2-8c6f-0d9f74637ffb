import { useState, useEffect } from "react";
import axios from "axios";
import "./App.css";
import { Button } from "./components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "./components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "./components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./components/ui/select";
import { useQuery } from "react-query";

// API URL - Configuração para desenvolvimento e produção
const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000/api";

// Tipos
interface Questao {
  id: number;
  pergunta: string;
  alternativas: string[];
  resposta_correta: number;
  categoria: string;
  subcategoria: string;
  dificuldade: string;
  explicacao: string;
  simulados: string[];
}

interface Categoria {
  id: string;
  nome: string;
  descricao: string;
  subcategorias: string[];
}

interface Simulado {
  id: string;
  nome: string;
  total_questoes: number;
  descricao: string;
}

interface Idioma {
  code: string;
  name: string;
  count: number;
}

interface DadosAPI {
  questoes: Questao[];
  categorias: Categoria[];
  simulados: Simulado[];
}

// Funções de API
const fetchDados = async (): Promise<DadosAPI> => {
  const response = await axios.get(`${API_URL}/questions`);
  return response.data;
};

const fetchQuestoesPorCategoria = async (
  categoria: string
): Promise<Questao[]> => {
  const response = await axios.get(
    `${API_URL}/questions/category/${categoria}`
  );
  return response.data;
};

const fetchQuestoesPorSimulado = async (
  simulado: string
): Promise<Questao[]> => {
  const response = await axios.get(`${API_URL}/questions/simulado/${simulado}`);
  return response.data;
};

const fetchQuestoesAleatorias = async (
  count: number,
  idioma?: string
): Promise<Questao[]> => {
  const params = idioma ? `?lang=${idioma}` : "";
  const response = await axios.get(
    `${API_URL}/questions/random/${count}${params}`
  );
  return response.data;
};

const fetchIdiomas = async (): Promise<Idioma[]> => {
  const response = await axios.get(`${API_URL}/languages`);
  return response.data;
};

function App() {
  // Estados
  const [modoAtual, setModoAtual] = useState("inicio");
  const [questoesSimulado, setQuestoesSimulado] = useState<Questao[]>([]);
  const [questaoAtual, setQuestaoAtual] = useState(0);
  const [respostas, setRespostas] = useState<number[]>([]);
  const [mostrarResultado, setMostrarResultado] = useState(false);
  const [categoriaAtual, setCategoriaAtual] = useState<string | null>(null);
  const [simuladoAtual, setSimuladoAtual] = useState<string | null>(null);
  const [tempoRestante, setTempoRestante] = useState<number | null>(null);
  const [feedbackImediato, setFeedbackImediato] = useState(false);
  const [respostaClicada, setRespostaClicada] = useState<number | null>(null);
  const [idiomaSelecionado, setIdiomaSelecionado] = useState<string>("pt-br");

  // Buscar dados da API
  const {
    data: dados,
    isLoading: carregando,
    error: erro,
  } = useQuery<DadosAPI, Error>("dados", fetchDados, {
    staleTime: 300000, // 5 minutos
    retry: 3,
  });

  // Buscar idiomas disponíveis
  const { data: idiomas, isLoading: carregandoIdiomas } = useQuery<
    Idioma[],
    Error
  >("idiomas", fetchIdiomas, {
    staleTime: 600000, // 10 minutos
    retry: 3,
  });

  // Timer para simulados com tempo
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    if (
      tempoRestante !== null &&
      tempoRestante > 0 &&
      modoAtual === "simulado" &&
      !mostrarResultado
    ) {
      timer = setInterval(() => {
        setTempoRestante((prev) => {
          if (prev === null || prev <= 1) {
            if (timer) clearInterval(timer);
            setMostrarResultado(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [tempoRestante, modoAtual, mostrarResultado]);

  // Iniciar simulado completo
  const iniciarSimuladoCompleto = async () => {
    try {
      const questoesAleatorias = await fetchQuestoesAleatorias(
        40,
        idiomaSelecionado
      );

      setQuestoesSimulado(questoesAleatorias);
      setRespostas(new Array(questoesAleatorias.length).fill(-1));
      setQuestaoAtual(0);
      setMostrarResultado(false);
      setTempoRestante(60 * 60); // 60 minutos em segundos
      setFeedbackImediato(false);
      setModoAtual("simulado");
    } catch (error) {
      console.error("Erro ao iniciar simulado completo:", error);
    }
  };

  // Iniciar simulado por categoria
  const iniciarSimuladoPorCategoria = async (
    categoria: string,
    numQuestoes: number = 10
  ) => {
    try {
      const questoesDaCategoria = await fetchQuestoesPorCategoria(categoria);
      const questoesSelecionadas = questoesDaCategoria
        .sort(() => 0.5 - Math.random())
        .slice(0, numQuestoes);

      setCategoriaAtual(categoria);
      setQuestoesSimulado(questoesSelecionadas);
      setRespostas(new Array(questoesSelecionadas.length).fill(-1));
      setQuestaoAtual(0);
      setMostrarResultado(false);
      setTempoRestante(null); // Sem limite de tempo
      setFeedbackImediato(true);
      setModoAtual("simulado");
    } catch (error) {
      console.error("Erro ao iniciar simulado por categoria:", error);
    }
  };

  // Iniciar simulado original
  const iniciarSimuladoOriginal = async (simuladoId: string) => {
    try {
      const questoesDoSimulado = await fetchQuestoesPorSimulado(simuladoId);

      setSimuladoAtual(simuladoId);
      setQuestoesSimulado(questoesDoSimulado);
      setRespostas(new Array(questoesDoSimulado.length).fill(-1));
      setQuestaoAtual(0);
      setMostrarResultado(false);
      setTempoRestante(null); // Sem limite de tempo
      setFeedbackImediato(false);
      setModoAtual("simulado");
    } catch (error) {
      console.error("Erro ao iniciar simulado original:", error);
    }
  };

  // Iniciar modo rápido
  const iniciarModoRapido = async () => {
    try {
      const questoesAleatorias = await fetchQuestoesAleatorias(
        5,
        idiomaSelecionado
      );

      setQuestoesSimulado(questoesAleatorias);
      setRespostas(new Array(questoesAleatorias.length).fill(-1));
      setQuestaoAtual(0);
      setMostrarResultado(false);
      setTempoRestante(null); // Sem limite de tempo
      setFeedbackImediato(true);
      setModoAtual("simulado");
    } catch (error) {
      console.error("Erro ao iniciar modo rápido:", error);
    }
  };

  // Responder questão
  const responderQuestao = (indiceResposta: number) => {
    // Permite alterar resposta mesmo se já respondida
    const novasRespostas = [...respostas];
    novasRespostas[questaoAtual] = indiceResposta;
    setRespostas(novasRespostas);

    if (feedbackImediato) {
      setRespostaClicada(indiceResposta);
      setTimeout(() => {
        setRespostaClicada(null);
        if (questaoAtual < questoesSimulado.length - 1) {
          setQuestaoAtual(questaoAtual + 1);
        } else {
          setMostrarResultado(true);
        }
      }, 5000); // Aumentado para 5 segundos (5000ms)
    }
  };

  // Navegar para próxima questão (permite pular sem responder)
  const proximaQuestao = () => {
    if (questaoAtual < questoesSimulado.length - 1) {
      setQuestaoAtual(questaoAtual + 1);
      setRespostaClicada(null); // Limpa feedback visual
    } else {
      setMostrarResultado(true);
    }
  };

  // Navegar para questão anterior
  const questaoAnterior = () => {
    if (questaoAtual > 0) {
      setQuestaoAtual(questaoAtual - 1);
      setRespostaClicada(null); // Limpa feedback visual
    }
  };

  // Finalizar simulado
  const finalizarSimulado = () => {
    setMostrarResultado(true);
  };

  // Voltar para o início
  const voltarParaInicio = () => {
    setModoAtual("inicio");
    setQuestoesSimulado([]);
    setRespostas([]);
    setQuestaoAtual(0);
    setMostrarResultado(false);
    setCategoriaAtual(null);
    setSimuladoAtual(null);
    setTempoRestante(null);
  };

  // Calcular resultado
  const calcularResultado = () => {
    if (!questoesSimulado.length)
      return { acertos: 0, total: 0, percentual: 0 };

    const acertos = respostas.reduce((total, resposta, index) => {
      return resposta === questoesSimulado[index].resposta_correta
        ? total + 1
        : total;
    }, 0);

    return {
      acertos,
      total: questoesSimulado.length,
      percentual: Math.round((acertos / questoesSimulado.length) * 100),
    };
  };

  // Renderizar tela de carregamento
  if (carregando) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4 text-foreground">
            Carregando...
          </h2>
          <p className="text-muted-foreground">Preparando seu ITILPrep Pro</p>
        </div>
      </div>
    );
  }

  // Renderizar tela de erro
  if (erro) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4 text-destructive">Erro</h2>
          <p className="text-foreground">{erro.message}</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Tentar Novamente
          </Button>
        </div>
      </div>
    );
  }

  // Dados mockados para quando a API não estiver disponível
  const dadosMock = {
    questoes: [
      {
        id: 1,
        pergunta:
          'Qual é o propósito da prática de "gerenciamento de nível de serviço"?',
        alternativas: [
          "Estabelecer e reforçar os vínculos entre a organização e as respectivas partes interessadas.",
          "Garantir que os fornecedores da organização e seu desempenho sejam gerenciados adequadamente.",
          "Suportar a qualidade acordada de um serviço por meio do tratamento de todas as requisições de serviço acordadas e iniciadas pelo usuários.",
          "Definir metas claras e baseadas no negócio para os níveis de serviço.",
        ],
        resposta_correta: 3,
        categoria: "praticas_gerenciamento",
        subcategoria: "gerenciamento_nivel_servico",
        dificuldade: "media",
        explicacao:
          "O propósito do gerenciamento de nível de serviço é definir metas claras baseadas no negócio para os níveis de serviço e garantir que os serviços sejam entregues de acordo com essas metas acordadas.",
        simulados: ["ITIL_4_DUMP_6_6"],
      },
      {
        id: 2,
        pergunta:
          "Qual prática ITIL recomenda a realização de revisões de serviço para garantir que os serviços continuem atendendo às necessidades da organização?",
        alternativas: [
          "Central de serviço.",
          "Gerenciamento de requisição de serviço.",
          "Gerenciamento de nível de serviço.",
          "Gerenciamento de configuração de serviço.",
        ],
        resposta_correta: 2,
        categoria: "praticas_gerenciamento",
        subcategoria: "gerenciamento_nivel_servico",
        dificuldade: "media",
        explicacao:
          "O gerenciamento de nível de serviço inclui a realização de revisões periódicas para garantir que os serviços continuem atendendo às necessidades da organização e dos clientes.",
        simulados: ["ITIL_4_DUMP_6_6"],
      },
      {
        id: 3,
        pergunta:
          "Uma organização pede a uma parte interessada para revisar uma mudança planejada. Isso demonstra qual princípio orientador?",
        alternativas: [
          "Colaborar e promover visibilidade.",
          "Começar de onde está.",
          "Foco no valor.",
          "Manter de forma simples e prática.",
        ],
        resposta_correta: 0,
        categoria: "principios_orientadores",
        subcategoria: "colaborar_promover_visibilidade",
        dificuldade: "facil",
        explicacao:
          "Pedir a uma parte interessada para revisar uma mudança planejada demonstra o princípio 'Colaborar e promover visibilidade', que enfatiza a importância da transparência e colaboração com todas as partes interessadas.",
        simulados: ["ITIL_4_DUMP_6_6"],
      },
    ],
    categorias: [
      {
        id: "conceitos_fundamentais",
        nome: "Conceitos Fundamentais do ITIL",
        descricao:
          "Definições básicas, Sistema de Valor de Serviço e Cadeia de Valor de Serviço",
        subcategorias: ["definicoes_basicas", "sistema_valor_servico"],
      },
      {
        id: "principios_orientadores",
        nome: "Princípios Orientadores",
        descricao: "Os 7 princípios orientadores do ITIL 4",
        subcategorias: ["foco_valor", "colaborar_promover_visibilidade"],
      },
      {
        id: "praticas_gerenciamento",
        nome: "Práticas de Gerenciamento",
        descricao:
          "Central de serviço, Gerenciamento de incidente, problema, requisição, etc.",
        subcategorias: ["central_servico", "gerenciamento_nivel_servico"],
      },
    ],
    simulados: [
      {
        id: "ITIL_4_DUMP_6_6",
        nome: "ITIL 4 DUMP 6/6",
        total_questoes: 40,
        descricao: "Simulado focado em conceitos fundamentais do ITIL 4",
      },
      {
        id: "ITIL_4_DUMP_5_6",
        nome: "ITIL 4 DUMP 5/6",
        total_questoes: 40,
        descricao: "Simulado focado em práticas de gerenciamento ITIL 4",
      },
    ],
  };

  // Renderizar tela inicial
  if (modoAtual === "inicio") {
    return (
      <div className="container mx-auto px-4 py-8 bg-background min-h-screen">
        <header className="text-center mb-12">
          <div className="flex justify-between items-start mb-6">
            <div className="flex-1"></div>
            <div className="flex-1 text-center">
              <h1 className="text-4xl font-bold mb-2 text-foreground">
                ITILPrep Pro
              </h1>
              <p className="text-xl text-muted-foreground">
                Prepare-se para sua certificação ITIL 4 com nossos simulados
                profissionais
              </p>
            </div>
            <div className="flex-1 flex justify-end">
              {/* Seletor de idioma removido - agora será por modo de simulado */}
            </div>
          </div>
        </header>

        <Tabs defaultValue="modos" className="max-w-4xl mx-auto">
          <TabsList className="grid grid-cols-3 mb-8">
            <TabsTrigger value="modos">Modos de Estudo</TabsTrigger>
            <TabsTrigger value="categorias">Categorias</TabsTrigger>
            <TabsTrigger value="simulados">Simulados Originais</TabsTrigger>
          </TabsList>

          <TabsContent value="modos">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Simulado Completo</CardTitle>
                  <CardDescription>
                    Simule a experiência real do exame ITIL
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    40 questões aleatórias com tempo limite de 60 minutos.
                    Feedback apenas ao final.
                  </p>
                </CardContent>
                <CardFooter>
                  <Button
                    onClick={() => {
                      setIdiomaSelecionado("pt-br");
                      iniciarSimuladoCompleto();
                    }}
                  >
                    Iniciar
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Modo Rápido</CardTitle>
                  <CardDescription>
                    Revisão rápida com feedback imediato
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    5 questões aleatórias com feedback após cada resposta. Ideal
                    para revisões rápidas.
                  </p>
                </CardContent>
                <CardFooter>
                  <Button
                    onClick={() => {
                      setIdiomaSelecionado("pt-br");
                      iniciarModoRapido();
                    }}
                  >
                    Iniciar
                  </Button>
                </CardFooter>
              </Card>

              {/* Simulados por idioma */}
              {idiomas &&
                idiomas.map((idioma) => (
                  <Card key={`simulado-${idioma.code}`}>
                    <CardHeader>
                      <CardTitle>
                        {idioma.code === "pt-br"
                          ? "Simulado em Português"
                          : idioma.code === "en"
                          ? "Simulado em Inglês"
                          : idioma.code === "es"
                          ? "Simulado em Espanhol"
                          : `Simulado em ${idioma.name}`}
                      </CardTitle>
                      <CardDescription>
                        Questões ITIL 4 em {idioma.name}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground">
                        {idioma.count} questões disponíveis. Prepare-se no
                        idioma de sua preferência.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <div className="flex gap-2 flex-wrap">
                        <Button
                          size="sm"
                          onClick={() => {
                            setIdiomaSelecionado(idioma.code);
                            iniciarModoRapido();
                          }}
                        >
                          Rápido (5)
                        </Button>
                        {idioma.count >= 40 && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setIdiomaSelecionado(idioma.code);
                              iniciarSimuladoCompleto();
                            }}
                          >
                            Completo (40)
                          </Button>
                        )}
                      </div>
                    </CardFooter>
                  </Card>
                ))}
            </div>
          </TabsContent>

          <TabsContent value="categorias">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {(dados?.categorias || dadosMock.categorias).map((categoria) => (
                <Card key={categoria.id}>
                  <CardHeader>
                    <CardTitle>{categoria.nome}</CardTitle>
                    <CardDescription>{categoria.descricao}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      Estude questões específicas sobre{" "}
                      {categoria.nome.toLowerCase()}.
                    </p>
                  </CardContent>
                  <CardFooter>
                    <Button
                      onClick={() => iniciarSimuladoPorCategoria(categoria.id)}
                    >
                      Iniciar (10 questões)
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="simulados">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {(dados?.simulados || dadosMock.simulados).map((simulado) => (
                <Card key={simulado.id}>
                  <CardHeader>
                    <CardTitle>{simulado.nome}</CardTitle>
                    <CardDescription>{simulado.descricao}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      {simulado.total_questoes} questões no formato original.
                    </p>
                  </CardContent>
                  <CardFooter>
                    <Button
                      onClick={() => iniciarSimuladoOriginal(simulado.id)}
                    >
                      Iniciar
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  // Renderizar tela de simulado
  if (modoAtual === "simulado" && !mostrarResultado) {
    const questaoAtual_obj = questoesSimulado[questaoAtual];

    return (
      <div className="container mx-auto px-4 py-8 bg-white text-black">
        <header className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <Button
              variant="outline"
              onClick={voltarParaInicio}
              className="border-gray-300"
            >
              Voltar
            </Button>
            <div className="text-center">
              <h2 className="text-xl font-bold text-black">
                {categoriaAtual
                  ? (dados?.categorias || dadosMock.categorias).find(
                      (c) => c.id === categoriaAtual
                    )?.nome
                  : simuladoAtual
                  ? (dados?.simulados || dadosMock.simulados).find(
                      (s) => s.id === simuladoAtual
                    )?.nome
                  : "Simulado ITIL 4"}
              </h2>
              {tempoRestante && (
                <p className="text-sm text-black">
                  Tempo restante: {Math.floor(tempoRestante / 60)}:
                  {(tempoRestante % 60).toString().padStart(2, "0")}
                </p>
              )}
            </div>
            <Button variant="destructive" onClick={finalizarSimulado}>
              Finalizar
            </Button>
          </div>
          <div className="w-full bg-gray-200 h-2 rounded-full">
            <div
              className="bg-blue-600 h-2 rounded-full"
              style={{
                width: `${
                  ((questaoAtual + 1) / questoesSimulado.length) * 100
                }%`,
              }}
            ></div>
          </div>
          <p className="text-center mt-2 text-black">
            Questão {questaoAtual + 1} de {questoesSimulado.length}
          </p>
        </header>

        <Card className="mb-8 border-gray-200 bg-white">
          <CardHeader>
            <CardTitle className="text-black">
              {questaoAtual_obj.pergunta}
            </CardTitle>
            {questaoAtual_obj.dificuldade && (
              <CardDescription className="text-gray-600">
                Dificuldade:{" "}
                {questaoAtual_obj.dificuldade === "facil"
                  ? "Fácil"
                  : questaoAtual_obj.dificuldade === "media"
                  ? "Média"
                  : "Difícil"}
              </CardDescription>
            )}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {questaoAtual_obj.alternativas.map((alternativa, index) => (
                <div
                  key={index}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors
                    ${
                      respostas[questaoAtual] === index
                        ? "border-blue-500 bg-blue-50 text-black"
                        : "border-gray-200 hover:bg-gray-50 text-black"
                    }
                    ${
                      feedbackImediato && respostaClicada === index
                        ? index === questaoAtual_obj.resposta_correta
                          ? "border-green-500 bg-green-50"
                          : "border-red-500 bg-red-50"
                        : ""
                    }`}
                  onClick={() => responderQuestao(index)}
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full border border-gray-300 flex items-center justify-center mr-3 text-black">
                      {String.fromCharCode(65 + index)}
                    </div>
                    <div className="text-black">{alternativa}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={questaoAnterior}
              disabled={questaoAtual === 0}
              className="border-gray-300"
            >
              ← Anterior
            </Button>
            <div className="flex gap-2">
              {!feedbackImediato && (
                <Button
                  variant="outline"
                  onClick={proximaQuestao}
                  disabled={feedbackImediato && respostaClicada !== null}
                  className="border-gray-300"
                >
                  Pular
                </Button>
              )}
              <Button
                onClick={proximaQuestao}
                disabled={feedbackImediato && respostaClicada !== null}
              >
                {questaoAtual === questoesSimulado.length - 1
                  ? "Finalizar"
                  : "Próxima →"}
              </Button>
            </div>
          </CardFooter>
        </Card>

        {feedbackImediato && respostaClicada !== null && (
          <Card
            className={`mb-4 
            ${
              respostaClicada === questaoAtual_obj.resposta_correta
                ? "border-green-500 bg-white"
                : "border-red-500 bg-white"
            }`}
          >
            <CardHeader>
              <CardTitle
                className={
                  respostaClicada === questaoAtual_obj.resposta_correta
                    ? "text-green-600"
                    : "text-red-600"
                }
              >
                {respostaClicada === questaoAtual_obj.resposta_correta
                  ? "Correto!"
                  : "Incorreto!"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-2 text-black">
                {respostaClicada !== questaoAtual_obj.resposta_correta && (
                  <span>
                    A resposta correta é:{" "}
                    <strong>
                      {String.fromCharCode(
                        65 + questaoAtual_obj.resposta_correta
                      )}
                    </strong>
                  </span>
                )}
              </p>
              <p className="text-black">{questaoAtual_obj.explicacao}</p>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  // Renderizar tela de resultado
  if (modoAtual === "simulado" && mostrarResultado) {
    const resultado = calcularResultado();
    const aprovado = resultado.percentual >= 70; // Considerando 70% como nota de aprovação

    return (
      <div className="container mx-auto px-4 py-8 bg-white">
        <Card className="max-w-2xl mx-auto border-gray-200 bg-white">
          <CardHeader>
            <CardTitle className="text-center text-2xl text-black">
              Resultado do Simulado
            </CardTitle>
            <CardDescription className="text-center text-gray-600">
              {categoriaAtual
                ? (dados?.categorias || dadosMock.categorias).find(
                    (c) => c.id === categoriaAtual
                  )?.nome
                : simuladoAtual
                ? (dados?.simulados || dadosMock.simulados).find(
                    (s) => s.id === simuladoAtual
                  )?.nome
                : "Simulado ITIL 4"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center mb-8">
              <div
                className={`text-5xl font-bold mb-2 ${
                  aprovado ? "text-green-600" : "text-red-600"
                }`}
              >
                {resultado.percentual}%
              </div>
              <p className="text-xl mb-4 text-black">
                {aprovado ? "Aprovado!" : "Não aprovado"}
              </p>
              <p className="text-black">
                Você acertou <strong>{resultado.acertos}</strong> de{" "}
                <strong>{resultado.total}</strong> questões.
              </p>
            </div>

            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-black">
                Revisão das Questões:
              </h3>

              {questoesSimulado.map((questao, index) => (
                <div
                  key={index}
                  className="border rounded-lg p-4 border-gray-200"
                >
                  <div className="flex items-start mb-2">
                    <div
                      className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                        respostas[index] === questao.resposta_correta
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {respostas[index] === questao.resposta_correta
                        ? "✓"
                        : "✗"}
                    </div>
                    <div className="font-medium text-black">
                      {questao.pergunta}
                    </div>
                  </div>

                  <div className="ml-8 mb-2">
                    <p className="text-black">
                      Sua resposta:{" "}
                      <span
                        className={
                          respostas[index] === questao.resposta_correta
                            ? "text-green-600"
                            : "text-red-600"
                        }
                      >
                        {String.fromCharCode(65 + respostas[index])}.{" "}
                        {questao.alternativas[respostas[index]]}
                      </span>
                    </p>

                    {respostas[index] !== questao.resposta_correta && (
                      <p className="text-green-600">
                        Resposta correta:{" "}
                        {String.fromCharCode(65 + questao.resposta_correta)}.{" "}
                        {questao.alternativas[questao.resposta_correta]}
                      </p>
                    )}
                  </div>

                  <div className="ml-8 text-sm text-gray-600">
                    <p>{questao.explicacao}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={voltarParaInicio}>Voltar ao Início</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return null;
}

export default App;
