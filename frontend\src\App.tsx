import { useState, useEffect } from "react";
import axios from "axios";
import "./App.css";
import { But<PERSON> } from "./components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "./components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "./components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./components/ui/select";
import { useQuery } from "react-query";

// Layout Components
import Header from "./components/Layout/Header";
import Sidebar from "./components/Layout/Sidebar";
import Footer from "./components/Layout/Footer";

// Section Components
import HomeSection from "./components/Sections/HomeSection";
import KnowledgeBaseSection from "./components/Sections/KnowledgeBaseSection";
import StudyTipsSection from "./components/Sections/StudyTipsSection";
import SourcesSection from "./components/Sections/SourcesSection";
import PracticeSection from "./components/Sections/PracticeSection";
import SimulationSection from "./components/Sections/SimulationSection";

// API URL - Configuração para desenvolvimento e produção
const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000/api";

// Tipos
interface Questao {
  id: number;
  pergunta: string;
  alternativas: string[];
  resposta_correta: number;
  categoria: string;
  subcategoria: string;
  dificuldade: string;
  explicacao: string;
  simulados: string[];
}

interface Categoria {
  id: string;
  nome: string;
  descricao: string;
  subcategorias: string[];
}

interface Simulado {
  id: string;
  nome: string;
  total_questoes: number;
  descricao: string;
}

interface Idioma {
  code: string;
  name: string;
  count: number;
}

interface DadosAPI {
  questoes: Questao[];
  categorias: Categoria[];
  simulados: Simulado[];
}

// Funções de API
const fetchDados = async (): Promise<DadosAPI> => {
  const response = await axios.get(`${API_URL}/questions`);
  return response.data;
};

const fetchQuestoesPorCategoria = async (
  categoria: string
): Promise<Questao[]> => {
  const response = await axios.get(
    `${API_URL}/questions/category/${categoria}`
  );
  return response.data;
};

const fetchQuestoesPorSimulado = async (
  simulado: string
): Promise<Questao[]> => {
  const response = await axios.get(`${API_URL}/questions/simulado/${simulado}`);
  return response.data;
};

const fetchQuestoesAleatorias = async (
  count: number,
  idioma?: string
): Promise<Questao[]> => {
  const params = idioma ? `?lang=${idioma}` : "";
  const response = await axios.get(
    `${API_URL}/questions/random/${count}${params}`
  );
  return response.data;
};

const fetchIdiomas = async (): Promise<Idioma[]> => {
  const response = await axios.get(`${API_URL}/languages`);
  return response.data;
};

function App() {
  // Estados principais
  const [currentSection, setCurrentSection] = useState("home");
  const [showSidebar, setShowSidebar] = useState(true);

  // Estados do simulado
  const [modoAtual, setModoAtual] = useState("inicio");
  const [questoesSimulado, setQuestoesSimulado] = useState<Questao[]>([]);
  const [questaoAtual, setQuestaoAtual] = useState(0);
  const [respostas, setRespostas] = useState<number[]>([]);
  const [mostrarResultado, setMostrarResultado] = useState(false);
  const [categoriaAtual, setCategoriaAtual] = useState<string | null>(null);
  const [simuladoAtual, setSimuladoAtual] = useState<string | null>(null);
  const [tempoRestante, setTempoRestante] = useState<number | null>(null);
  const [feedbackImediato, setFeedbackImediato] = useState(false);
  const [respostaClicada, setRespostaClicada] = useState<number | null>(null);
  const [idiomaSelecionado, setIdiomaSelecionado] = useState<string>("pt-br");

  // Buscar dados da API
  const {
    data: dados,
    isLoading: carregando,
    error: erro,
  } = useQuery<DadosAPI, Error>("dados", fetchDados, {
    staleTime: 300000, // 5 minutos
    retry: 3,
  });

  // Buscar idiomas disponíveis
  const { data: idiomas, isLoading: carregandoIdiomas } = useQuery<
    Idioma[],
    Error
  >("idiomas", fetchIdiomas, {
    staleTime: 600000, // 10 minutos
    retry: 3,
  });

  // Timer para simulados com tempo
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    if (
      tempoRestante !== null &&
      tempoRestante > 0 &&
      modoAtual === "simulado" &&
      !mostrarResultado
    ) {
      timer = setInterval(() => {
        setTempoRestante((prev) => {
          if (prev === null || prev <= 1) {
            if (timer) clearInterval(timer);
            setMostrarResultado(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [tempoRestante, modoAtual, mostrarResultado]);

  // Iniciar simulado completo
  const iniciarSimuladoCompleto = async () => {
    try {
      const questoesAleatorias = await fetchQuestoesAleatorias(
        40,
        idiomaSelecionado
      );

      setQuestoesSimulado(questoesAleatorias);
      setRespostas(new Array(questoesAleatorias.length).fill(-1));
      setQuestaoAtual(0);
      setMostrarResultado(false);
      setTempoRestante(60 * 60); // 60 minutos em segundos
      setFeedbackImediato(false);
      setModoAtual("simulado");
      setCurrentSection("simulation");
    } catch (error) {
      console.error("Erro ao iniciar simulado completo:", error);
    }
  };

  // Iniciar simulado por categoria
  const iniciarSimuladoPorCategoria = async (
    categoria: string,
    numQuestoes: number = 10
  ) => {
    try {
      const questoesDaCategoria = await fetchQuestoesPorCategoria(categoria);
      const questoesSelecionadas = questoesDaCategoria
        .sort(() => 0.5 - Math.random())
        .slice(0, numQuestoes);

      setCategoriaAtual(categoria);
      setQuestoesSimulado(questoesSelecionadas);
      setRespostas(new Array(questoesSelecionadas.length).fill(-1));
      setQuestaoAtual(0);
      setMostrarResultado(false);
      setTempoRestante(null); // Sem limite de tempo
      setFeedbackImediato(true);
      setModoAtual("simulado");
      setCurrentSection("simulation");
    } catch (error) {
      console.error("Erro ao iniciar simulado por categoria:", error);
    }
  };

  // Iniciar simulado original
  const iniciarSimuladoOriginal = async (simuladoId: string) => {
    try {
      const questoesDoSimulado = await fetchQuestoesPorSimulado(simuladoId);

      setSimuladoAtual(simuladoId);
      setQuestoesSimulado(questoesDoSimulado);
      setRespostas(new Array(questoesDoSimulado.length).fill(-1));
      setQuestaoAtual(0);
      setMostrarResultado(false);
      setTempoRestante(null); // Sem limite de tempo
      setFeedbackImediato(false);
      setModoAtual("simulado");
    } catch (error) {
      console.error("Erro ao iniciar simulado original:", error);
    }
  };

  // Iniciar modo rápido
  const iniciarModoRapido = async () => {
    try {
      const questoesAleatorias = await fetchQuestoesAleatorias(
        5,
        idiomaSelecionado
      );

      setQuestoesSimulado(questoesAleatorias);
      setRespostas(new Array(questoesAleatorias.length).fill(-1));
      setQuestaoAtual(0);
      setMostrarResultado(false);
      setTempoRestante(null); // Sem limite de tempo
      setFeedbackImediato(true);
      setModoAtual("simulado");
      setCurrentSection("simulation");
    } catch (error) {
      console.error("Erro ao iniciar modo rápido:", error);
    }
  };

  // Responder questão
  const responderQuestao = (indiceResposta: number) => {
    // Permite alterar resposta mesmo se já respondida
    const novasRespostas = [...respostas];
    novasRespostas[questaoAtual] = indiceResposta;
    setRespostas(novasRespostas);

    if (feedbackImediato) {
      setRespostaClicada(indiceResposta);
      setTimeout(() => {
        setRespostaClicada(null);
        if (questaoAtual < questoesSimulado.length - 1) {
          setQuestaoAtual(questaoAtual + 1);
        } else {
          setMostrarResultado(true);
        }
      }, 5000); // Aumentado para 5 segundos (5000ms)
    }
  };

  // Navegar para próxima questão (permite pular sem responder)
  const proximaQuestao = () => {
    if (questaoAtual < questoesSimulado.length - 1) {
      setQuestaoAtual(questaoAtual + 1);
      setRespostaClicada(null); // Limpa feedback visual
    } else {
      setMostrarResultado(true);
    }
  };

  // Navegar para questão anterior
  const questaoAnterior = () => {
    if (questaoAtual > 0) {
      setQuestaoAtual(questaoAtual - 1);
      setRespostaClicada(null); // Limpa feedback visual
    }
  };

  // Finalizar simulado
  const finalizarSimulado = () => {
    setMostrarResultado(true);
  };

  // Voltar para o início
  const voltarParaInicio = () => {
    setModoAtual("inicio");
    setQuestoesSimulado([]);
    setRespostas([]);
    setQuestaoAtual(0);
    setMostrarResultado(false);
    setCategoriaAtual(null);
    setSimuladoAtual(null);
    setTempoRestante(null);
    setCurrentSection("home");
  };

  // Função para mudar seção
  const handleSectionChange = (section: string) => {
    setCurrentSection(section);
    if (section !== "practice" && section !== "simulation") {
      // Reset simulado quando sair das seções de prática
      setModoAtual("inicio");
    }
  };

  // Calcular resultado
  const calcularResultado = () => {
    if (!questoesSimulado.length)
      return { acertos: 0, total: 0, percentual: 0 };

    const acertos = respostas.reduce((total, resposta, index) => {
      return resposta === questoesSimulado[index].resposta_correta
        ? total + 1
        : total;
    }, 0);

    return {
      acertos,
      total: questoesSimulado.length,
      percentual: Math.round((acertos / questoesSimulado.length) * 100),
    };
  };

  // Renderizar tela de carregamento
  if (carregando) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4 text-foreground">
            Carregando...
          </h2>
          <p className="text-muted-foreground">Preparando seu ITILPrep Pro</p>
        </div>
      </div>
    );
  }

  // Renderizar tela de erro
  if (erro) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4 text-destructive">Erro</h2>
          <p className="text-foreground">{erro.message}</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Tentar Novamente
          </Button>
        </div>
      </div>
    );
  }

  // Dados mockados para quando a API não estiver disponível
  const dadosMock = {
    questoes: [
      {
        id: 1,
        pergunta:
          'Qual é o propósito da prática de "gerenciamento de nível de serviço"?',
        alternativas: [
          "Estabelecer e reforçar os vínculos entre a organização e as respectivas partes interessadas.",
          "Garantir que os fornecedores da organização e seu desempenho sejam gerenciados adequadamente.",
          "Suportar a qualidade acordada de um serviço por meio do tratamento de todas as requisições de serviço acordadas e iniciadas pelo usuários.",
          "Definir metas claras e baseadas no negócio para os níveis de serviço.",
        ],
        resposta_correta: 3,
        categoria: "praticas_gerenciamento",
        subcategoria: "gerenciamento_nivel_servico",
        dificuldade: "media",
        explicacao:
          "O propósito do gerenciamento de nível de serviço é definir metas claras baseadas no negócio para os níveis de serviço e garantir que os serviços sejam entregues de acordo com essas metas acordadas.",
        simulados: ["ITIL_4_DUMP_6_6"],
      },
      {
        id: 2,
        pergunta:
          "Qual prática ITIL recomenda a realização de revisões de serviço para garantir que os serviços continuem atendendo às necessidades da organização?",
        alternativas: [
          "Central de serviço.",
          "Gerenciamento de requisição de serviço.",
          "Gerenciamento de nível de serviço.",
          "Gerenciamento de configuração de serviço.",
        ],
        resposta_correta: 2,
        categoria: "praticas_gerenciamento",
        subcategoria: "gerenciamento_nivel_servico",
        dificuldade: "media",
        explicacao:
          "O gerenciamento de nível de serviço inclui a realização de revisões periódicas para garantir que os serviços continuem atendendo às necessidades da organização e dos clientes.",
        simulados: ["ITIL_4_DUMP_6_6"],
      },
      {
        id: 3,
        pergunta:
          "Uma organização pede a uma parte interessada para revisar uma mudança planejada. Isso demonstra qual princípio orientador?",
        alternativas: [
          "Colaborar e promover visibilidade.",
          "Começar de onde está.",
          "Foco no valor.",
          "Manter de forma simples e prática.",
        ],
        resposta_correta: 0,
        categoria: "principios_orientadores",
        subcategoria: "colaborar_promover_visibilidade",
        dificuldade: "facil",
        explicacao:
          "Pedir a uma parte interessada para revisar uma mudança planejada demonstra o princípio 'Colaborar e promover visibilidade', que enfatiza a importância da transparência e colaboração com todas as partes interessadas.",
        simulados: ["ITIL_4_DUMP_6_6"],
      },
    ],
    categorias: [
      {
        id: "conceitos_fundamentais",
        nome: "Conceitos Fundamentais do ITIL",
        descricao:
          "Definições básicas, Sistema de Valor de Serviço e Cadeia de Valor de Serviço",
        subcategorias: ["definicoes_basicas", "sistema_valor_servico"],
      },
      {
        id: "principios_orientadores",
        nome: "Princípios Orientadores",
        descricao: "Os 7 princípios orientadores do ITIL 4",
        subcategorias: ["foco_valor", "colaborar_promover_visibilidade"],
      },
      {
        id: "praticas_gerenciamento",
        nome: "Práticas de Gerenciamento",
        descricao:
          "Central de serviço, Gerenciamento de incidente, problema, requisição, etc.",
        subcategorias: ["central_servico", "gerenciamento_nivel_servico"],
      },
    ],
    simulados: [
      {
        id: "ITIL_4_DUMP_6_6",
        nome: "ITIL 4 DUMP 6/6",
        total_questoes: 40,
        descricao: "Simulado focado em conceitos fundamentais do ITIL 4",
      },
      {
        id: "ITIL_4_DUMP_5_6",
        nome: "ITIL 4 DUMP 5/6",
        total_questoes: 40,
        descricao: "Simulado focado em práticas de gerenciamento ITIL 4",
      },
    ],
  };

  // Renderizar conteúdo principal
  const renderMainContent = () => {
    // Se estamos em modo simulado, renderizar o componente de simulação
    if (modoAtual === "simulado") {
      return (
        <SimulationSection
          questoesSimulado={questoesSimulado}
          questaoAtual={questaoAtual}
          respostas={respostas}
          mostrarResultado={mostrarResultado}
          tempoRestante={tempoRestante}
          feedbackImediato={feedbackImediato}
          respostaClicada={respostaClicada}
          responderQuestao={responderQuestao}
          proximaQuestao={proximaQuestao}
          questaoAnterior={questaoAnterior}
          finalizarSimulado={finalizarSimulado}
          voltarParaInicio={voltarParaInicio}
          calcularResultado={calcularResultado}
        />
      );
    }

    // Renderizar seções baseadas no currentSection
    switch (currentSection) {
      case "home":
        return (
          <HomeSection
            onSectionChange={handleSectionChange}
            idiomas={idiomas}
          />
        );
      case "knowledge":
        return <KnowledgeBaseSection onSectionChange={handleSectionChange} />;
      case "tips":
        return <StudyTipsSection onSectionChange={handleSectionChange} />;
      case "sources":
        return <SourcesSection onSectionChange={handleSectionChange} />;
      case "practice":
        return (
          <PracticeSection
            onSectionChange={handleSectionChange}
            idiomas={idiomas}
            dados={dados}
            iniciarSimuladoCompleto={iniciarSimuladoCompleto}
            iniciarModoRapido={iniciarModoRapido}
            iniciarSimuladoPorCategoria={iniciarSimuladoPorCategoria}
            iniciarSimuladoOriginal={iniciarSimuladoOriginal}
            setIdiomaSelecionado={setIdiomaSelecionado}
          />
        );
      default:
        return (
          <HomeSection
            onSectionChange={handleSectionChange}
            idiomas={idiomas}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <Header
        currentSection={currentSection}
        onSectionChange={handleSectionChange}
      />

      {/* Main Layout */}
      <div className="flex flex-1">
        {/* Main Content */}
        <main className="flex-1 p-6 overflow-y-auto">
          {renderMainContent()}
        </main>

        {/* Sidebar - Hide during simulation */}
        {modoAtual !== "simulado" && showSidebar && (
          <Sidebar onSectionChange={handleSectionChange} />
        )}
      </div>

      {/* Footer - Hide during simulation */}
      {modoAtual !== "simulado" && (
        <Footer onSectionChange={handleSectionChange} />
      )}
    </div>
  );
}

export default App;
