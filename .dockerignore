# Arquivos e diretórios a serem ignorados pelo Docker

# Node.js
frontend/node_modules
frontend/dist
frontend/.vite
frontend/coverage

# Python
backend/venv
backend/__pycache__
backend/*.pyc
backend/*.pyo
backend/*.pyd
backend/.Python
backend/env
backend/pip-log.txt
backend/pip-delete-this-directory.txt
backend/.tox
backend/.coverage
backend/.coverage.*
backend/.cache
backend/nosetests.xml
backend/coverage.xml
backend/*.cover
backend/*.log
backend/.git
backend/.mypy_cache
backend/.pytest_cache
backend/.hypothesis
backend/htmlcov

# IDEs
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production

# parcel-bundler cache
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# Documentation
README.md
LICENSE
*.md

# Scripts
scripts/

# Test files
**/*.test.*
**/*.spec.*
test/
tests/

# Build artifacts
build/
dist/

# Temporary files
tmp/
temp/
