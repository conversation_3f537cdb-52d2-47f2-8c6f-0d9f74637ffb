#!/usr/bin/env python3
"""
Script para extrair questões ITIL 4 de múltiplas fontes Daypo e integrá-las ao banco de dados.
"""

import json
import os
import re
from typing import List, Dict, <PERSON><PERSON>

def parse_daypo_question(question_text: str, question_id: int, source: str) -> Dict:
    """
    Processa uma questão do formato Daypo e converte para o formato do banco de dados.
    """
    # Divide a questão em pergunta e alternativas
    parts = question_text.split('. ')
    
    if len(parts) < 5:  # Pergunta + 4 alternativas mínimo
        return None
    
    # Extrai a pergunta (primeira parte)
    pergunta = parts[0].strip()
    if pergunta.endswith('?'):
        pergunta = pergunta
    else:
        pergunta += "?"
    
    # Extrai as alternativas (próximas 4 partes)
    alternativas = []
    for i in range(1, min(5, len(parts))):
        alt = parts[i].strip()
        if alt and not alt.startswith('1 e ') and not alt.startswith('2 e '):
            alternativas.append(alt)
    
    # Se não conseguiu extrair 4 alternativas, tenta método alternativo
    if len(alternativas) < 4:
        # Tenta dividir por padrões específicos
        text = question_text
        # Remove a pergunta
        if '?' in text:
            pergunta = text.split('?')[0] + '?'
            remaining = text.split('?', 1)[1] if '?' in text else text
        else:
            pergunta = text.split('.')[0] + '?'
            remaining = '.'.join(text.split('.')[1:])
        
        # Extrai alternativas por padrões
        alternativas = []
        # Padrão: texto. texto. texto. texto.
        alt_parts = remaining.split('. ')
        for part in alt_parts:
            part = part.strip()
            if part and len(part) > 3 and not part.startswith('1 e ') and not part.startswith('2 e '):
                alternativas.append(part)
                if len(alternativas) >= 4:
                    break
    
    if len(alternativas) < 4:
        return None
    
    # Determina a resposta correta (sempre a primeira por padrão, será ajustado manualmente se necessário)
    resposta_correta = 0
    
    # Categoriza automaticamente baseado no conteúdo
    categoria, subcategoria = categorize_question(pergunta, alternativas)
    
    # Determina dificuldade baseada na complexidade
    dificuldade = determine_difficulty(pergunta, alternativas)
    
    return {
        "id": question_id,
        "pergunta": pergunta,
        "alternativas": alternativas[:4],  # Garante apenas 4 alternativas
        "resposta_correta": resposta_correta,
        "categoria": categoria,
        "subcategoria": subcategoria,
        "dificuldade": dificuldade,
        "explicacao": f"Questão extraída do {source} sobre {categoria.replace('_', ' ')}",
        "simulados": [source.replace(' ', '_').upper()],
        "idioma": "pt-br",
        "fonte": "Daypo Comprehensive"
    }

def categorize_question(pergunta: str, alternativas: List[str]) -> Tuple[str, str]:
    """
    Categoriza automaticamente uma questão baseada no conteúdo.
    """
    text = (pergunta + ' ' + ' '.join(alternativas)).lower()
    
    # Princípios orientadores
    if any(word in text for word in ['princípio', 'orientador', 'foco no valor', 'colaborar', 'simples', 'prática', 'iterativo', 'feedback', 'holístico', 'otimizar', 'automatizar']):
        if 'foco no valor' in text or 'valor' in text:
            return 'principios_orientadores', 'foco_valor'
        elif 'colaborar' in text or 'visibilidade' in text:
            return 'principios_orientadores', 'colaborar_promover_visibilidade'
        elif 'simples' in text or 'prática' in text:
            return 'principios_orientadores', 'manter_simples_pratico'
        elif 'iterativo' in text or 'feedback' in text:
            return 'principios_orientadores', 'progredir_iterativamente'
        elif 'holístico' in text or 'holisticamente' in text:
            return 'principios_orientadores', 'pensar_trabalhar_holisticamente'
        elif 'otimizar' in text or 'automatizar' in text:
            return 'principios_orientadores', 'otimizar_automatizar'
        else:
            return 'principios_orientadores', 'comecar_onde_esta'
    
    # Práticas de gerenciamento
    elif any(word in text for word in ['prática', 'gerenciamento', 'central', 'serviço', 'incidente', 'problema', 'mudança', 'requisição']):
        if 'central' in text and 'serviço' in text:
            return 'praticas_gerenciamento', 'central_servico'
        elif 'incidente' in text:
            return 'praticas_gerenciamento', 'gerenciamento_incidente'
        elif 'problema' in text:
            return 'praticas_gerenciamento', 'gerenciamento_problema'
        elif 'mudança' in text or 'habilitação' in text:
            return 'praticas_gerenciamento', 'habilitacao_mudanca'
        elif 'requisição' in text:
            return 'praticas_gerenciamento', 'gerenciamento_requisicao_servico'
        elif 'nível' in text and 'serviço' in text:
            return 'praticas_gerenciamento', 'gerenciamento_nivel_servico'
        elif 'configuração' in text:
            return 'praticas_gerenciamento', 'gerenciamento_configuracao_servico'
        elif 'liberação' in text or 'release' in text:
            return 'praticas_gerenciamento', 'gerenciamento_liberacao'
        elif 'implantação' in text or 'deployment' in text:
            return 'praticas_gerenciamento', 'gerenciamento_implantacao'
        else:
            return 'praticas_gerenciamento', 'central_servico'
    
    # Quatro dimensões
    elif any(word in text for word in ['dimensão', 'organização', 'pessoas', 'informação', 'tecnologia', 'parceiros', 'fornecedores', 'fluxo', 'processos']):
        if 'organização' in text or 'pessoas' in text:
            return 'quatro_dimensoes', 'organizacoes_pessoas'
        elif 'informação' in text or 'tecnologia' in text:
            return 'quatro_dimensoes', 'informacao_tecnologia'
        elif 'parceiros' in text or 'fornecedores' in text:
            return 'quatro_dimensoes', 'parceiros_fornecedores'
        else:
            return 'quatro_dimensoes', 'fluxo_valor_processos'
    
    # Melhoria contínua
    elif any(word in text for word in ['melhoria', 'contínua', 'modelo', 'rmc', 'fofa', 'swot']):
        if 'modelo' in text:
            return 'melhoria_continua', 'modelo_melhoria_continua'
        elif 'registro' in text or 'rmc' in text:
            return 'melhoria_continua', 'registro_melhoria_continua'
        else:
            return 'melhoria_continua', 'modelo_melhoria_continua'
    
    # Conceitos fundamentais (padrão)
    else:
        if any(word in text for word in ['sistema', 'valor', 'cadeia', 'svs']):
            return 'conceitos_fundamentais', 'sistema_valor_servico'
        else:
            return 'conceitos_fundamentais', 'definicoes_basicas'

def determine_difficulty(pergunta: str, alternativas: List[str]) -> str:
    """
    Determina a dificuldade da questão baseada na complexidade.
    """
    text = (pergunta + ' ' + ' '.join(alternativas)).lower()
    
    # Palavras que indicam dificuldade alta
    hard_indicators = ['análise', 'avaliação', 'implementação', 'estratégico', 'tático', 'complexo', 'múltiplas', 'integração']
    
    # Palavras que indicam dificuldade média
    medium_indicators = ['processo', 'procedimento', 'método', 'técnica', 'abordagem', 'relacionamento']
    
    if any(word in text for word in hard_indicators):
        return 'dificil'
    elif any(word in text for word in medium_indicators):
        return 'media'
    else:
        return 'facil'

def extract_daypo_comprehensive():
    """
    Extrai questões de múltiplas fontes Daypo e integra ao banco de dados.
    """
    
    # Questões do ITIL 4 TODAS 07-2023 (já extraídas anteriormente, mas vamos adicionar mais)
    daypo_todas_questions = [
        "Quais decisões de gerenciamento de requisição de serviço exigem que politicas sejam estabelecidas?. Decidir como as degradações de serviço são resolvidas. Decidir como lidar com requisições de serviço em que as etapas são desconhecidas. Decidir quais requisições de serviços exigem aprovação. Decidir quando soluções de contorno devem ser usadas.",
        
        "A equipe de uma organização de TI está muito ocupada, principalmente realizando tarefas que adicionam pouco ou nenhum valor à organização ou aos clientes. Qual o princípio orientador recomenda que o trabalho desnecessário seja eliminado?. Manter de forma simples e prática. Pensar e trabalhar holisticamente. Começar de onde você está. Progredir interativamente com feedback.",
        
        "Qual prática faz uso de métodos de Lean, Agile e DevOps?. Central de serviço. Melhoria contínua. Gerenciamento de problema. Gerenciamento de incidente.",
        
        "Qual das opções é um exemplo de requisição de serviço?. Uma requisição para a operação normal ser restaurada. Uma requisição para implementar uma correção de segurança. Uma requisição para acesso a um arquivo. Uma requisição para investigar a causa de um incidente.",
        
        "O propósito de qual prática inclui a criação de relacionamentos mais próximos e colaborativos?. Gerenciamento de fornecedor. Gerenciamento de segurança da informação. Gerenciamento de liberação. Gerenciamento de configuração de serviço.",
        
        "Qual fase do gerenciamento de problemas inclui a avaliação normal da efetividade de soluções de contorno?. Identificação de problemas. Controle de problemas. Controle de erros. Análise de problemas.",
        
        "Um usuário entra em contato com a central de serviço para perguntar como se cria um relatório. Qual prática tem MAIS PROBABILIDADE de contribuir para resolver essa questão?. Gerenciamento de incidente. Gerenciamento de nível de serviço. Gerenciamento de requisição de serviço. Habilitação de mudança.",
        
        "O que é definido como qualquer componente que precisa ser gerenciado a fim de entregar um serviço de TI?. Um evento. Um ativo de TI. Um item de configuração. Uma mudança.",
        
        "Qual destas afirmativas sobre ofertas de serviço está CORRETA?. O mesmo produto pode ser usado como base para mais de uma oferta de serviço. As ofertas de serviço incluem a transferência de bens do consumidor para o provedor. As ofertas de serviço descrevem como os provedores e consumidores cooperam para cocriar valor. Cada serviço deve ser descrito para os consumidores como uma oferta de serviço única.",
        
        "Uma falha em um aplicativo pode causar a falha de um serviço. A equipe de TI está analisando ativamente o aplicativo para tentar entender o que está acontecendo. Qual é o nome correto desse tipo de falha?. Problema. Incidente. Evento. Erro conhecido."
    ]
    
    # Questões do DUMP 3/6
    dump3_questions = [
        "O que é um ativo de TI?. A remoção de qualquer coisa que possa ter um efeito direto ou indireto nos serviços. Qualquer componente que precise ser gerenciado para fornecer um serviço. Uma solicitação de um usuário que inicia uma ação de serviço. Qualquer componente financeiramente valioso que possa contribuir para a entrega de um produto ou serviço de TI.",
        
        "Qual dimensão inclui um sistema de gerenciamento de fluxo de trabalho?. Fluxo de valor e processos. Parceiros e fornecedores. Informação e tecnologia. Organizações e pessoas.",
        
        "Qual das situações a seguir deve ser registrada e gerenciada como um problema?. Um usuário solicita a entrega de um notebook. Uma ferramenta de monitoração detecta uma mudança de estado em um serviço. A análise de tendência apresenta um número grande de incidentes similares. A melhoria contínua precisa priorizar uma oportunidade de melhoria.",
        
        "Qual princípio orientador recomenda coordenar todas as dimensões do gerenciamento de serviços?. Comece de onde está. Pensar e trabalhar holisticamente. Manter de forma simples e prática. Progredir iterativamente com feedback.",
        
        "Qual o propósito da prática de gerenciamento do relacionamento?. Estabelecer e reforçar os vínculos entre a organização e as respectivas partes interessadas. Alinhar as práticas e os serviços da organização com as necessidades de negócio em constante mudança. Definir metas claras e baseadas nos negócios para o desempenho dos serviços. Suportar a qualidade acordada de um serviço ao tratar de todas as requisições de serviço acordadas e iniciadas pelos usuários.",
        
        "Como o fluxo de trabalho de uma nova requisição de serviço deve ser desenhado?. Usando um novo fluxo de trabalho para todos os tipos de requisição de serviço. Usando fluxos de trabalho diferentes para cada tipo de requisição de serviço. Evitando os fluxos de trabalho para requisição de serviço simples. Aproveitando os fluxos de trabalho existentes sempre que possível.",
        
        "Qual é o propósito da prática gerenciamento da segurança da informação?. Proteger as informações de que a organização precisa para conduzir seus negócios. Observar os serviços e componente de serviços. Garantir que informações precisas e confiáveis sobre a configuração dos serviços estejam disponíveis quando e onde forem necessárias. Planejar e gerenciar o ciclo de vida completo de todos os ativos de TI.",
        
        "Como a automação deve ser implementada?. Ao se concentrar inicialmente nas tarefas mais complexas. Otimizando o máximo possível primeiro. Substituindo a intervenção humana sempre que possível. Substituindo as ferramentas existentes primeiro.",
        
        "Qual das atividades a seguir faz parte da prática de melhoria contínua?. Identificação e registro de oportunidades. Engajamento tático e operacional com os clientes. Preenchimento e manutenção do registro de ativo. Fornecimento de um caminho claro para os usuários relatarem questões, fazerem consultas e enviarem requisições.",
        
        "Qual prática aplica técnicas como análise FOFA (SWOT), revisões de indicadores balanceados de desempenho (balanced scorecard) e avaliações de maturidade?. Gerenciamento de incidente. Gerenciamento de problema. Melhoria contínua. Gerenciamento de requisição de serviço."
    ]
    
    # Carrega banco de dados existente
    questions_file = os.path.join(os.path.dirname(__file__), 'data', 'questions.json')
    
    try:
        with open(questions_file, 'r', encoding='utf-8') as f:
            database = json.load(f)
    except FileNotFoundError:
        database = {'questoes': [], 'categorias': [], 'simulados': []}
    
    # Processa todas as questões
    all_questions = daypo_todas_questions + dump3_questions
    
    # ID inicial para novas questões
    existing_ids = {q['id'] for q in database['questoes']}
    next_id = max(existing_ids) + 1 if existing_ids else 4000
    
    new_questions = []
    
    for i, question_text in enumerate(all_questions):
        question_data = parse_daypo_question(question_text, next_id + i, "DAYPO_COMPREHENSIVE")
        if question_data:
            new_questions.append(question_data)
    
    # Adiciona questões que não existem
    for question in new_questions:
        # Verifica se questão similar já existe
        exists = False
        for existing_q in database['questoes']:
            if existing_q['pergunta'].lower().strip() == question['pergunta'].lower().strip():
                exists = True
                break
        
        if not exists:
            database['questoes'].append(question)
    
    # Adiciona simulado se não existir
    comprehensive_simulado = {
        'id': 'DAYPO_COMPREHENSIVE',
        'nome': 'Daypo ITIL 4 Comprehensive Collection',
        'total_questoes': len([q for q in database['questoes'] if 'DAYPO_COMPREHENSIVE' in q.get('simulados', [])]),
        'descricao': 'Coleção abrangente de questões ITIL 4 extraídas de múltiplas fontes Daypo'
    }
    
    # Verifica se simulado já existe
    simulado_exists = False
    for i, sim in enumerate(database['simulados']):
        if sim['id'] == 'DAYPO_COMPREHENSIVE':
            database['simulados'][i] = comprehensive_simulado
            simulado_exists = True
            break
    
    if not simulado_exists:
        database['simulados'].append(comprehensive_simulado)
    
    # Salva banco de dados atualizado
    with open(questions_file, 'w', encoding='utf-8') as f:
        json.dump(database, f, ensure_ascii=False, indent=2)
    
    print(f"Processadas {len(new_questions)} questões do Daypo Comprehensive")
    print(f"Total de questões no banco: {len(database['questoes'])}")
    
    # Mostra estatísticas por categoria
    categorias = {}
    for q in database['questoes']:
        cat = q.get('categoria', 'unknown')
        categorias[cat] = categorias.get(cat, 0) + 1
    
    print("\nEstatísticas por categoria:")
    for cat, count in categorias.items():
        print(f"  {cat.replace('_', ' ').title()}: {count} questões")

if __name__ == '__main__':
    extract_daypo_comprehensive()
