# 📚 ITILPrep Pro - Aplicativo de Estudo ITIL 4

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Node.js](https://img.shields.io/badge/Node.js-16+-green.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/React-18.3+-blue.svg)](https://reactjs.org/)
[![Flask](https://img.shields.io/badge/Flask-3.1+-red.svg)](https://flask.palletsprojects.com/)

Um aplicativo web interativo e moderno para estudar para a certificação ITIL 4, desenvolvido com React + TypeScript (frontend) e Flask (backend). O aplicativo oferece diferentes modos de estudo, feedback detalhado e uma interface intuitiva para maximizar seu aprendizado.

## ✨ Características

### 🎯 **Modos de Estudo Diversificados**

- **Simulado Completo**: 40 questões aleatórias com tempo limitado de 60 minutos
- **Simulado por Categoria**: Foco em tópicos específicos do ITIL 4
- **Simulado Original**: Reprodução fiel dos testes originais
- **Modo Rápido**: 5 questões com feedback imediato para revisões rápidas

### 📊 **Recursos de Aprendizado Avançados**

- **Feedback Detalhado**: Explicações completas para cada questão
- **Estatísticas de Desempenho**: Acompanhe seu progresso
- **Sistema de Revisão**: Revise questões incorretas
- **Interface Responsiva**: Funciona perfeitamente em desktop e mobile
- **Modo Escuro**: Suporte automático baseado na preferência do sistema

### 🛠️ **Tecnologias Modernas**

- **Frontend**: React 18 + TypeScript + Vite + TailwindCSS
- **Backend**: Flask + Python com API REST
- **UI Components**: Radix UI para acessibilidade
- **Testes**: Vitest + Testing Library + Pytest
- **Qualidade de Código**: ESLint + TypeScript + Black

## 📁 Estrutura do Projeto

```
itilprep-pro/
├── 📁 frontend/                    # Frontend React + TypeScript
│   ├── 📁 src/
│   │   ├── 📁 components/ui/       # Componentes UI (Radix)
│   │   ├── 📁 hooks/              # Custom hooks
│   │   ├── 📁 lib/                # Utilitários
│   │   ├── 📁 test/               # Configuração de testes
│   │   ├── App.tsx                # Componente principal
│   │   └── main.tsx               # Ponto de entrada
│   ├── 📄 package.json            # Dependências e scripts
│   ├── 📄 vite.config.ts          # Configuração do Vite
│   ├── 📄 tailwind.config.js      # Configuração do TailwindCSS
│   └── 📄 tsconfig.json           # Configuração do TypeScript
│
├── 📁 backend/                     # Backend Flask + Python
│   ├── 📁 src/
│   │   ├── 📁 data/               # Dados das questões (JSON)
│   │   ├── 📁 static/             # Arquivos estáticos (build do frontend)
│   │   └── 📄 main.py             # API Flask
│   ├── 📄 requirements.txt        # Dependências de produção
│   ├── 📄 requirements-dev.txt    # Dependências de desenvolvimento
│   ├── 📄 test_main.py            # Testes da API
│   └── 📄 .env.example            # Exemplo de variáveis de ambiente
│
├── 📄 README.md                   # Documentação do projeto
├── 📄 .gitignore                  # Arquivos ignorados pelo Git
└── 📄 LICENSE                     # Licença do projeto
```

## 🔧 Pré-requisitos

Antes de começar, certifique-se de ter instalado:

### 🌐 **Frontend**

- **Node.js** 16+ ([Download](https://nodejs.org/))
- **npm** ou **pnpm** (incluído com Node.js)

### 🐍 **Backend**

- **Python** 3.8+ ([Download](https://www.python.org/downloads/))
- **pip** (incluído com Python)

### 🛠️ **Ferramentas Opcionais**

- **Git** para controle de versão
- **VS Code** com extensões Python e TypeScript

## 🚀 Instalação e Configuração

### 1️⃣ **Clone o Repositório**

```bash
git clone <url-do-repositorio>
cd itilprep-pro
```

### 2️⃣ **Configuração do Frontend**

```bash
cd frontend

# Instalar dependências
npm install
# ou se preferir pnpm
pnpm install

# Verificar se tudo está funcionando
npm run dev
```

### 3️⃣ **Configuração do Backend**

```bash
cd backend

# Criar ambiente virtual
python -m venv venv

# Ativar ambiente virtual
# No Windows:
venv\Scripts\activate
# No Linux/Mac:
source venv/bin/activate

# Instalar dependências
pip install -r requirements.txt

# Para desenvolvimento (inclui ferramentas de teste)
pip install -r requirements-dev.txt

# Configurar variáveis de ambiente (opcional)
cp .env.example .env
```

## ▶️ Execução

### 🔧 **Modo Desenvolvimento**

Execute o frontend e backend simultaneamente em terminais separados:

**Terminal 1 - Frontend:**

```bash
cd frontend
npm run dev
# Aplicação estará disponível em: http://localhost:3000
```

**Terminal 2 - Backend:**

```bash
cd backend
# Ativar ambiente virtual
source venv/bin/activate  # Windows: venv\Scripts\activate

# Executar API
python src/main.py
# API estará disponível em: http://localhost:5000
```

### 🚀 **Modo Produção**

**1. Compilar o Frontend:**

```bash
cd frontend
npm run build
```

**2. Copiar Build para o Backend:**

```bash
# Linux/Mac:
cp -r frontend/dist/* backend/src/static/

# Windows:
xcopy frontend\dist\* backend\src\static\ /E /Y
```

**3. Executar Servidor de Produção:**

```bash
cd backend
source venv/bin/activate  # Windows: venv\Scripts\activate

# Usando Flask (desenvolvimento)
python src/main.py

# Usando Gunicorn (produção - Linux/Mac)
gunicorn -w 4 -b 0.0.0.0:5000 src.main:app
```

## 🧪 Testes

### **Frontend (Vitest + Testing Library)**

```bash
cd frontend

# Executar todos os testes
npm run test

# Executar testes em modo watch
npm run test -- --watch

# Executar testes com interface gráfica
npm run test:ui

# Gerar relatório de cobertura
npm run test:coverage
```

### **Backend (Pytest)**

```bash
cd backend

# Executar todos os testes
pytest

# Executar testes com cobertura
pytest --cov=src

# Executar testes específicos
pytest test_main.py::TestAPIEndpoints::test_get_questions

# Executar testes em modo verbose
pytest -v
```

### **Qualidade de Código**

```bash
# Frontend - Linting
cd frontend
npm run lint

# Backend - Formatação e linting
cd backend
black src/
flake8 src/
mypy src/
```

## 📊 Dados e Personalização

### **Estrutura dos Dados**

O aplicativo utiliza um arquivo JSON (`backend/src/data/questions.json`) com a seguinte estrutura:

```json
{
  "questoes": [
    {
      "id": 1,
      "pergunta": "Qual é o propósito do ITIL?",
      "alternativas": ["Opção A", "Opção B", "Opção C", "Opção D"],
      "resposta_correta": 0,
      "categoria": "conceitos_fundamentais",
      "subcategoria": "definicoes_basicas",
      "dificuldade": "facil",
      "explicacao": "Explicação detalhada da resposta",
      "simulados": ["ITIL_4_DUMP_1"]
    }
  ],
  "categorias": [...],
  "simulados": [...]
}
```

### **Personalização**

- **📝 Questões**: Edite `backend/src/data/questions.json`
- **🎨 Aparência**: Modifique `frontend/src/index.css` e componentes
- **⚙️ Configurações**: Ajuste `backend/.env` e `frontend/vite.config.ts`

## 🔗 API Endpoints

| Método | Endpoint                              | Descrição              |
| ------ | ------------------------------------- | ---------------------- |
| GET    | `/api/health`                         | Status da API          |
| GET    | `/api/questions`                      | Todas as questões      |
| GET    | `/api/questions/category/{categoria}` | Questões por categoria |
| GET    | `/api/questions/simulado/{simulado}`  | Questões por simulado  |
| GET    | `/api/questions/random/{count}`       | Questões aleatórias    |
| GET    | `/api/categories`                     | Todas as categorias    |
| GET    | `/api/simulados`                      | Todos os simulados     |

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🆘 Suporte

- 📧 **Email**: [<EMAIL>]
- 🐛 **Issues**: [GitHub Issues](https://github.com/seu-usuario/simulado-itil-4/issues)
- 💬 **Discussões**: [GitHub Discussions](https://github.com/seu-usuario/simulado-itil-4/discussions)

## 🙏 Agradecimentos

- Comunidade ITIL pela documentação e recursos
- Contribuidores do projeto
- Bibliotecas e frameworks utilizados

---

⭐ **Se este projeto foi útil para você, considere dar uma estrela no GitHub!**
