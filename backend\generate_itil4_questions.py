#!/usr/bin/env python3
"""
Gerador de questões ITIL 4 de alta qualidade
Objetivo: Criar questões abrangentes para todos os tópicos do ITIL 4 Foundation
"""

import json
from typing import List, Dict, Any

def generate_comprehensive_itil4_questions() -> List[Dict[str, Any]]:
    """
    Gera questões abrangentes do ITIL 4 Foundation
    """
    questions = []
    
    # Conceitos Fundamentais
    fundamental_questions = [
        {
            "id": 3001,
            "pergunta": "Qual é a definição de 'Serviço' no ITIL 4?",
            "alternativas": [
                "Um meio de entregar valor aos clientes facilitando os resultados que os clientes querem alcançar",
                "Um conjunto de processos de TI automatizados",
                "Uma aplicação de software que suporta processos de negócio",
                "Um contrato entre provedor e cliente de serviços"
            ],
            "resposta_correta": 0,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "definicoes_basicas",
            "dificuldade": "facil",
            "explicacao": "Um serviço é um meio de entregar valor aos clientes, facilitando os resultados que os clientes querem alcançar sem que eles tenham que gerenciar custos e riscos específicos.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 3002,
            "pergunta": "O que é 'Garantia' no contexto de valor de serviço?",
            "alternativas": [
                "A funcionalidade oferecida pelo serviço",
                "A segurança dos dados do cliente",
                "Como o serviço é entregue (disponibilidade, capacidade, segurança, continuidade)",
                "O contrato de nível de serviço"
            ],
            "resposta_correta": 2,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "valor_servicos",
            "dificuldade": "media",
            "explicacao": "Garantia refere-se a como o serviço é entregue, incluindo aspectos como disponibilidade, capacidade, segurança e continuidade do serviço.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 3003,
            "pergunta": "Quais são os componentes principais do Sistema de Valor de Serviço (SVS)?",
            "alternativas": [
                "Processos, funções e papéis",
                "Princípios orientadores, governança, cadeia de valor, práticas, melhoria contínua",
                "Incidentes, problemas e mudanças",
                "Pessoas, processos e tecnologia"
            ],
            "resposta_correta": 1,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "sistema_valor_servico",
            "dificuldade": "media",
            "explicacao": "O SVS inclui os princípios orientadores, governança, cadeia de valor de serviço, práticas e melhoria contínua.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        }
    ]
    
    # Princípios Orientadores
    principles_questions = [
        {
            "id": 3004,
            "pergunta": "Qual princípio orientador enfatiza que 'tudo que a organização faz deve mapear para valor'?",
            "alternativas": [
                "Começar de onde você está",
                "Foco no valor",
                "Progredir iterativamente com feedback",
                "Colaborar e promover visibilidade"
            ],
            "resposta_correta": 1,
            "categoria": "principios_orientadores",
            "subcategoria": "foco_valor",
            "dificuldade": "facil",
            "explicacao": "O princípio 'Foco no valor' estabelece que tudo que a organização faz deve mapear, direta ou indiretamente, para valor para as partes interessadas.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 3005,
            "pergunta": "O princípio 'Começar de onde você está' significa:",
            "alternativas": [
                "Sempre implementar soluções completamente novas",
                "Observar o que já existe e construir sobre isso",
                "Ignorar sistemas legados",
                "Começar com orçamento zero"
            ],
            "resposta_correta": 1,
            "categoria": "principios_orientadores",
            "subcategoria": "comecar_onde_esta",
            "dificuldade": "media",
            "explicacao": "Este princípio enfatiza não começar do zero, mas observar o que já existe e construir sobre isso, preservando o que é valioso.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 3006,
            "pergunta": "Qual princípio orientador sugere 'usar o número mínimo de etapas para atingir um objetivo'?",
            "alternativas": [
                "Otimizar e automatizar",
                "Manter simples e prático",
                "Pensar e trabalhar holisticamente",
                "Progredir iterativamente com feedback"
            ],
            "resposta_correta": 1,
            "categoria": "principios_orientadores",
            "subcategoria": "simples_pratico",
            "dificuldade": "media",
            "explicacao": "O princípio 'Manter simples e prático' enfatiza usar o número mínimo de etapas para atingir um objetivo e eliminar atividades que não agregam valor.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        }
    ]
    
    # Quatro Dimensões
    dimensions_questions = [
        {
            "id": 3007,
            "pergunta": "A dimensão 'Organizações e pessoas' inclui:",
            "alternativas": [
                "Apenas a estrutura organizacional",
                "Estrutura organizacional, cultura, papéis, responsabilidades e competências",
                "Somente recursos humanos",
                "Apenas processos de negócio"
            ],
            "resposta_correta": 1,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "quatro_dimensoes",
            "dificuldade": "media",
            "explicacao": "A dimensão 'Organizações e pessoas' abrange estrutura organizacional, cultura, papéis, responsabilidades, competências e habilidades necessárias.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 3008,
            "pergunta": "Qual dimensão se concentra em 'como as várias partes da organização trabalham de forma integrada'?",
            "alternativas": [
                "Organizações e pessoas",
                "Informação e tecnologia",
                "Parceiros e fornecedores",
                "Fluxos de valor e processos"
            ],
            "resposta_correta": 3,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "quatro_dimensoes",
            "dificuldade": "media",
            "explicacao": "A dimensão 'Fluxos de valor e processos' foca em como as várias partes da organização trabalham de forma integrada e coordenada para entregar valor.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        }
    ]
    
    # Práticas de Gerenciamento
    practices_questions = [
        {
            "id": 3009,
            "pergunta": "Qual é o objetivo principal da Central de Serviços?",
            "alternativas": [
                "Resolver todos os problemas de TI",
                "Ser o ponto único de contato entre provedor e usuários",
                "Gerenciar mudanças no ambiente",
                "Monitorar a performance dos serviços"
            ],
            "resposta_correta": 1,
            "categoria": "praticas_gerenciamento",
            "subcategoria": "central_servicos",
            "dificuldade": "facil",
            "explicacao": "A Central de Serviços é o ponto único de contato entre o provedor de serviços e os usuários para questões, consultas e requisições de serviço.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 3010,
            "pergunta": "Qual é a diferença principal entre Incidente e Problema?",
            "alternativas": [
                "Não há diferença, são sinônimos",
                "Incidente é uma interrupção não planejada; Problema é a causa de um ou mais incidentes",
                "Problema é mais urgente que Incidente",
                "Incidente afeta usuários; Problema afeta sistemas"
            ],
            "resposta_correta": 1,
            "categoria": "praticas_gerenciamento",
            "subcategoria": "incidentes_problemas",
            "dificuldade": "media",
            "explicacao": "Um incidente é uma interrupção não planejada ou redução na qualidade de um serviço. Um problema é a causa de um ou mais incidentes.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        }
    ]
    
    # Cadeia de Valor de Serviço
    value_chain_questions = [
        {
            "id": 3011,
            "pergunta": "Quantas atividades da cadeia de valor existem no ITIL 4?",
            "alternativas": [
                "5 atividades",
                "6 atividades",
                "7 atividades",
                "8 atividades"
            ],
            "resposta_correta": 1,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "cadeia_valor",
            "dificuldade": "facil",
            "explicacao": "A cadeia de valor de serviço do ITIL 4 possui 6 atividades: Planejar, Melhorar, Engajar, Design e transição, Obter/construir, Entregar e suportar.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 3012,
            "pergunta": "Qual atividade da cadeia de valor garante entendimento compartilhado da visão?",
            "alternativas": [
                "Engajar",
                "Planejar",
                "Melhorar",
                "Design e transição"
            ],
            "resposta_correta": 1,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "cadeia_valor",
            "dificuldade": "media",
            "explicacao": "A atividade 'Planejar' garante entendimento compartilhado da visão, status atual e direção de melhoria para todos os produtos e serviços.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        }
    ]
    
    # Melhoria Contínua
    improvement_questions = [
        {
            "id": 3013,
            "pergunta": "Qual é a primeira pergunta do modelo de melhoria contínua?",
            "alternativas": [
                "Onde estamos agora?",
                "Qual é a visão?",
                "Como chegamos lá?",
                "Onde queremos estar?"
            ],
            "resposta_correta": 1,
            "categoria": "melhoria_continua",
            "subcategoria": "modelo_melhoria",
            "dificuldade": "media",
            "explicacao": "O modelo de melhoria contínua começa com 'Qual é a visão?' para estabelecer a direção e objetivos da melhoria.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 3014,
            "pergunta": "O que é o Registro de Melhoria Contínua (RMC)?",
            "alternativas": [
                "Um relatório de incidentes",
                "Um banco de dados para registrar oportunidades de melhoria",
                "Um sistema de monitoramento",
                "Um processo de aprovação de mudanças"
            ],
            "resposta_correta": 1,
            "categoria": "melhoria_continua",
            "subcategoria": "ferramentas",
            "dificuldade": "media",
            "explicacao": "O RMC é um banco de dados ou lista estruturada usada para registrar e gerenciar oportunidades de melhoria ao longo de seu ciclo de vida.",
            "simulados": ["ITIL4_FOUNDATION"],
            "idioma": "pt-br",
            "fonte": "ITIL 4 Foundation Official"
        }
    ]
    
    # Combinar todas as questões
    questions.extend(fundamental_questions)
    questions.extend(principles_questions)
    questions.extend(dimensions_questions)
    questions.extend(practices_questions)
    questions.extend(value_chain_questions)
    questions.extend(improvement_questions)
    
    return questions

def main():
    """Função principal"""
    print("=== Geração de Questões ITIL 4 Abrangentes ===")
    
    # Gerar questões
    questions = generate_comprehensive_itil4_questions()
    
    print(f"Total de questões geradas: {len(questions)}")
    
    # Salvar questões
    output_file = "questoes_itil4_completas.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(questions, f, ensure_ascii=False, indent=2)
    
    print(f"Questões salvas em: {output_file}")
    
    # Estatísticas
    categorias = {}
    dificuldades = {}
    
    for q in questions:
        cat = q.get('categoria', 'unknown')
        dif = q.get('dificuldade', 'unknown')
        
        categorias[cat] = categorias.get(cat, 0) + 1
        dificuldades[dif] = dificuldades.get(dif, 0) + 1
    
    print("\nDistribuição por categoria:")
    for cat, count in categorias.items():
        print(f"  {cat}: {count} questões")
    
    print("\nDistribuição por dificuldade:")
    for dif, count in dificuldades.items():
        print(f"  {dif}: {count} questões")

if __name__ == "__main__":
    main()
