import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from 'react-query'
import axios from 'axios'
import App from '../App'

// Mock do axios
vi.mock('axios')
const mockedAxios = vi.mocked(axios)

// <PERSON><PERSON> mock para testes
const mockData = {
  questoes: [
    {
      id: 1,
      pergunta: "Qual é o propósito do ITIL?",
      alternativas: ["Opção A", "Opção B", "Opção C", "Opção D"],
      resposta_correta: 0,
      categoria: "conceitos_fundamentais",
      subcategoria: "definicoes_basicas",
      dificuldade: "facil",
      explicacao: "Explicação da resposta",
      simulados: ["ITIL_4_DUMP_1"]
    }
  ],
  categorias: [
    {
      id: "conceitos_fundamentais",
      nome: "Conceitos Fundamentais",
      descricao: "Conceitos básicos do ITIL",
      subcategorias: ["definicoes_basicas"]
    }
  ],
  simulados: [
    {
      id: "ITIL_4_DUMP_1",
      nome: "ITIL 4 DUMP 1",
      total_questoes: 1,
      descricao: "Simulado de teste"
    }
  ]
}

// Componente wrapper para testes
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('App Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Mock da resposta padrão do axios
    mockedAxios.get.mockResolvedValue({ data: mockData })
  })

  it('deve renderizar o título principal', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(screen.getByText('Simulado ITIL 4')).toBeInTheDocument()
    })
  })

  it('deve renderizar a descrição do aplicativo', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(screen.getByText('Prepare-se para sua certificação ITIL com nossos simulados interativos')).toBeInTheDocument()
    })
  })

  it('deve renderizar as abas de navegação', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(screen.getByText('Modos de Estudo')).toBeInTheDocument()
      expect(screen.getByText('Categorias')).toBeInTheDocument()
      expect(screen.getByText('Simulados Originais')).toBeInTheDocument()
    })
  })

  it('deve renderizar os cards de modos de estudo', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(screen.getByText('Simulado Completo')).toBeInTheDocument()
      expect(screen.getByText('Modo Rápido')).toBeInTheDocument()
    })
  })

  it('deve exibir tela de carregamento inicialmente', () => {
    // Mock para simular carregamento
    mockedAxios.get.mockImplementation(() => new Promise(() => {}))

    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )

    expect(screen.getByText('Carregando...')).toBeInTheDocument()
    expect(screen.getByText('Preparando seu simulado ITIL')).toBeInTheDocument()
  })

  it('deve exibir tela de erro quando a API falha', async () => {
    mockedAxios.get.mockRejectedValue(new Error('Erro de rede'))

    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(screen.getByText('Erro')).toBeInTheDocument()
      expect(screen.getByText('Tentar Novamente')).toBeInTheDocument()
    })
  })

  it('deve iniciar simulado completo quando botão é clicado', async () => {
    // Mock para questões aleatórias
    mockedAxios.get.mockImplementation((url) => {
      if (url.includes('/api/questions/random/40')) {
        return Promise.resolve({ data: [mockData.questoes[0]] })
      }
      return Promise.resolve({ data: mockData })
    })

    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(screen.getByText('Simulado Completo')).toBeInTheDocument()
    })

    const iniciarButton = screen.getAllByText('Iniciar')[0]
    fireEvent.click(iniciarButton)

    await waitFor(() => {
      expect(screen.getByText('Qual é o propósito do ITIL?')).toBeInTheDocument()
    })
  })

  it('deve navegar entre abas corretamente', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(screen.getByText('Modos de Estudo')).toBeInTheDocument()
    })

    // Clica na aba de categorias
    const categoriasTab = screen.getByText('Categorias')
    fireEvent.click(categoriasTab)

    await waitFor(() => {
      expect(screen.getByText('Conceitos Fundamentais')).toBeInTheDocument()
    })

    // Clica na aba de simulados
    const simuladosTab = screen.getByText('Simulados Originais')
    fireEvent.click(simuladosTab)

    await waitFor(() => {
      expect(screen.getByText('ITIL 4 DUMP 1')).toBeInTheDocument()
    })
  })

  it('deve responder questão corretamente', async () => {
    // Mock para questões aleatórias
    mockedAxios.get.mockImplementation((url) => {
      if (url.includes('/api/questions/random/5')) {
        return Promise.resolve({ data: [mockData.questoes[0]] })
      }
      return Promise.resolve({ data: mockData })
    })

    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(screen.getByText('Modo Rápido')).toBeInTheDocument()
    })

    // Inicia modo rápido
    const modoRapidoButton = screen.getAllByText('Iniciar')[1]
    fireEvent.click(modoRapidoButton)

    await waitFor(() => {
      expect(screen.getByText('Qual é o propósito do ITIL?')).toBeInTheDocument()
    })

    // Clica na primeira opção
    const opcaoA = screen.getByText('Opção A')
    fireEvent.click(opcaoA)

    // Verifica se a resposta foi selecionada
    await waitFor(() => {
      expect(screen.getByText('Correto!')).toBeInTheDocument()
    })
  })
})

describe('App Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('deve fazer chamadas corretas para a API', async () => {
    mockedAxios.get.mockResolvedValue({ data: mockData })

    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/questions')
    })
  })

  it('deve usar dados mock quando API não está disponível', async () => {
    mockedAxios.get.mockRejectedValue(new Error('API não disponível'))

    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )

    // Mesmo com erro na API, deve usar dados mock
    await waitFor(() => {
      expect(screen.getByText('Erro')).toBeInTheDocument()
    })
  })
})
