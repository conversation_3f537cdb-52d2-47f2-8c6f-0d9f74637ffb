import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import {
  Clock,
  Target,
  BookOpen,
  Brain,
  CheckCircle,
  Calendar,
  Lightbulb,
  TrendingUp,
  Users,
  Award,
  Video,
  ExternalLink,
} from "lucide-react";
import Breadcrumb from "../Layout/Breadcrumb";

interface StudyTipsSectionProps {
  onSectionChange: (section: string) => void;
}

const StudyTipsSection: React.FC<StudyTipsSectionProps> = ({
  onSectionChange,
}) => {
  const breadcrumbItems = [{ label: "Dicas de Estudo", active: true }];

  const studyStrategies = [
    {
      icon: Clock,
      title: "Gestão de Tempo",
      tips: [
        "Dedique 1-2 horas diárias de estudo consistente",
        "Use a técnica Pomodoro: 25 min estudo + 5 min pausa",
        "Estude nos horários em que você tem mais energia",
        "Reserve tempo para revisões semanais",
      ],
    },
    {
      icon: Brain,
      title: "Técnicas de Memorização",
      tips: [
        "Crie mapas mentais para os conceitos ITIL 4",
        "Use flashcards para definições importantes",
        "Pratique a explicação dos conceitos em voz alta",
        "Associe conceitos a exemplos práticos do trabalho",
      ],
    },
    {
      icon: Target,
      title: "Estratégia de Simulados",
      tips: [
        "Comece com simulados por categoria",
        "Faça simulados completos semanalmente",
        "Analise todas as respostas, certas e erradas",
        "Mantenha um log de seus pontos fracos",
      ],
    },
    {
      icon: BookOpen,
      title: "Estudo Ativo",
      tips: [
        "Não apenas leia, mas questione o conteúdo",
        "Faça resumos dos principais conceitos",
        "Discuta tópicos com colegas ou grupos de estudo",
        "Aplique conceitos ITIL em situações reais",
      ],
    },
  ];

  const learningStyles = [
    {
      type: "Visual",
      icon: "👁️",
      description: "Aprende melhor com diagramas, mapas mentais e cores",
      tips: [
        "Use diagramas para entender os fluxos ITIL",
        "Crie mapas mentais coloridos",
        "Destaque conceitos importantes com cores",
        "Assista vídeos explicativos",
      ],
    },
    {
      type: "Auditivo",
      icon: "👂",
      description: "Aprende melhor ouvindo e falando",
      tips: [
        "Grave resumos e ouça durante deslocamentos",
        "Participe de grupos de discussão",
        "Explique conceitos em voz alta",
        "Use podcasts sobre ITIL",
      ],
    },
    {
      type: "Cinestésico",
      icon: "✋",
      description: "Aprende melhor fazendo e praticando",
      tips: [
        "Faça muitos simulados práticos",
        "Escreva resumos à mão",
        "Use objetos físicos para representar conceitos",
        "Caminhe enquanto revisa",
      ],
    },
  ];

  const studySchedule = [
    {
      week: "Semanas 1-2",
      focus: "Conceitos Fundamentais",
      activities: [
        "Leitura básica",
        "Simulados por categoria",
        "Mapas mentais",
      ],
    },
    {
      week: "Semanas 3-4",
      focus: "Princípios Orientadores",
      activities: ["Estudo detalhado", "Exemplos práticos", "Discussões"],
    },
    {
      week: "Semanas 5-6",
      focus: "Práticas de Gerenciamento",
      activities: ["Simulados focados", "Casos de uso", "Revisões"],
    },
    {
      week: "Semanas 7-8",
      focus: "Revisão e Simulados",
      activities: [
        "Simulados completos",
        "Revisão de pontos fracos",
        "Preparação final",
      ],
    },
  ];

  return (
    <div className="space-y-6">
      <Breadcrumb items={breadcrumbItems} onSectionChange={onSectionChange} />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Dicas de Estudo para ITIL 4
        </h1>
        <p className="text-lg text-gray-600">
          Estratégias comprovadas para maximizar seu aprendizado e conquistar a
          certificação
        </p>
      </div>

      {/* Exam Overview */}
      <Card className="border-l-4 border-l-green-500 bg-green-50">
        <CardHeader>
          <CardTitle className="flex items-center text-green-800">
            <Award className="w-5 h-5 mr-2" />
            Visão Geral do Exame ITIL 4 Foundation
          </CardTitle>
        </CardHeader>
        <CardContent className="text-green-700">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">40</div>
              <div className="text-sm">Questões</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">60</div>
              <div className="text-sm">Minutos</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">65%</div>
              <div className="text-sm">Nota Mínima</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">26</div>
              <div className="text-sm">Acertos Necessários</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Study Strategies */}
      <div>
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">
          Estratégias de Estudo
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {studyStrategies.map((strategy, index) => {
            const Icon = strategy.icon;
            return (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Icon className="w-5 h-5 mr-3 text-blue-600" />
                    {strategy.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {strategy.tips.map((tip, tipIndex) => (
                      <li key={tipIndex} className="flex items-start">
                        <CheckCircle className="w-4 h-4 mr-2 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{tip}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Learning Styles */}
      <div>
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">
          Estilos de Aprendizagem
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {learningStyles.map((style, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <span className="text-2xl mr-3">{style.icon}</span>
                  {style.type}
                </CardTitle>
                <CardDescription>{style.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {style.tips.map((tip, tipIndex) => (
                    <li key={tipIndex} className="flex items-start">
                      <Lightbulb className="w-4 h-4 mr-2 text-yellow-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{tip}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Study Schedule */}
      <div>
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">
          Cronograma de Estudo Sugerido (8 semanas)
        </h2>
        <div className="space-y-4">
          {studySchedule.map((period, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="w-5 h-5 mr-3 text-purple-600" />
                  {period.week}: {period.focus}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {period.activities.map((activity, actIndex) => (
                    <span
                      key={actIndex}
                      className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm"
                    >
                      {activity}
                    </span>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Final Tips */}
      <Card className="border-l-4 border-l-blue-500 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center text-blue-800">
            <TrendingUp className="w-5 h-5 mr-2" />
            Dicas Finais para o Sucesso
          </CardTitle>
        </CardHeader>
        <CardContent className="text-blue-700">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ul className="space-y-2">
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 mr-2 text-blue-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  Mantenha consistência nos estudos
                </span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 mr-2 text-blue-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  Foque na compreensão, não na memorização
                </span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 mr-2 text-blue-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  Pratique com simulados regularmente
                </span>
              </li>
            </ul>
            <ul className="space-y-2">
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 mr-2 text-blue-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm">Descanse bem antes do exame</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 mr-2 text-blue-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  Gerencie seu tempo durante o exame
                </span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-4 h-4 mr-2 text-blue-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm">Confie em sua preparação</span>
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Video Resources Section */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-l-4 border-l-purple-500">
        <CardHeader>
          <CardTitle className="text-purple-800 flex items-center">
            <Video className="w-6 h-6 mr-2" />
            Recursos em Vídeo Recomendados
          </CardTitle>
        </CardHeader>
        <CardContent className="text-purple-700">
          <p className="mb-4">
            Complemente seus estudos com estes excelentes recursos em vídeo
            sobre ITIL 4:
          </p>
          <div className="grid grid-cols-1 gap-4">
            <div className="bg-white p-4 rounded-lg border border-purple-200">
              <h4 className="font-semibold mb-2 flex items-center">
                <ExternalLink className="w-4 h-4 mr-2" />
                ITIL 4 Foundation Complete Course
              </h4>
              <p className="text-sm mb-2">
                Curso completo e abrangente sobre ITIL 4 Foundation
              </p>
              <a
                href="https://www.youtube.com/watch?v=zxfYQO-Je-8&t=6462s"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 text-sm underline"
              >
                Assistir no YouTube →
              </a>
            </div>
            <div className="bg-white p-4 rounded-lg border border-purple-200">
              <h4 className="font-semibold mb-2 flex items-center">
                <ExternalLink className="w-4 h-4 mr-2" />
                ITIL 4 Service Management
              </h4>
              <p className="text-sm mb-2">
                Conceitos fundamentais de gerenciamento de serviços
              </p>
              <a
                href="https://www.youtube.com/watch?v=QUjw2QKEuF8&t=961s"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 text-sm underline"
              >
                Assistir no YouTube →
              </a>
            </div>
            <div className="bg-white p-4 rounded-lg border border-purple-200">
              <h4 className="font-semibold mb-2 flex items-center">
                <ExternalLink className="w-4 h-4 mr-2" />
                ITIL 4 Fundamentals
              </h4>
              <p className="text-sm mb-2">
                Base conceitual e fundamentos do ITIL 4
              </p>
              <a
                href="https://www.youtube.com/watch?v=3Xm6eGzJoM4"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 text-sm underline"
              >
                Assistir no YouTube →
              </a>
            </div>
            <div className="bg-white p-4 rounded-lg border border-purple-200">
              <h4 className="font-semibold mb-2 flex items-center">
                <ExternalLink className="w-4 h-4 mr-2" />
                ITIL 4 Practices Overview
              </h4>
              <p className="text-sm mb-2">
                Visão geral das práticas de gerenciamento ITIL 4
              </p>
              <a
                href="https://www.youtube.com/watch?v=5FzNS7XsrVw"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 text-sm underline"
              >
                Assistir no YouTube →
              </a>
            </div>
            <div className="bg-white p-4 rounded-lg border border-purple-200">
              <h4 className="font-semibold mb-2 flex items-center">
                <ExternalLink className="w-4 h-4 mr-2" />
                ITIL 4 Value Chain
              </h4>
              <p className="text-sm mb-2">
                Cadeia de valor de serviço e suas 6 atividades
              </p>
              <a
                href="https://www.youtube.com/watch?v=qRJ08-owjMY"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 text-sm underline"
              >
                Assistir no YouTube →
              </a>
            </div>
            <div className="bg-white p-4 rounded-lg border border-purple-200">
              <h4 className="font-semibold mb-2 flex items-center">
                <ExternalLink className="w-4 h-4 mr-2" />
                ITIL 4 Guiding Principles
              </h4>
              <p className="text-sm mb-2">
                Os 7 princípios orientadores do ITIL 4
              </p>
              <a
                href="https://www.youtube.com/watch?v=m9YbzbOfr24"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 text-sm underline"
              >
                Assistir no YouTube →
              </a>
            </div>
            <div className="bg-white p-4 rounded-lg border border-purple-200">
              <h4 className="font-semibold mb-2 flex items-center">
                <ExternalLink className="w-4 h-4 mr-2" />
                Playlist Completa ITIL 4
              </h4>
              <p className="text-sm mb-2">
                Coleção completa de vídeos sobre ITIL 4
              </p>
              <a
                href="https://www.youtube.com/watch?v=QUjw2QKEuF8&list=PLcEPi2yN6Z0KG9nQa-Xv33tURfEOw53NR"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 text-sm underline"
              >
                Ver Playlist Completa →
              </a>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StudyTipsSection;
