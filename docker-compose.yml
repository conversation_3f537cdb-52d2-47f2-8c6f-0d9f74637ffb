version: '3.8'

services:
  simulado-itil:
    build: .
    container_name: simulado-itil-4
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=False
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=5000
    volumes:
      - ./backend/src/data:/app/src/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Serviço opcional para desenvolvimento com hot reload
  simulado-itil-dev:
    build: .
    container_name: simulado-itil-4-dev
    ports:
      - "5001:5000"
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=True
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=5000
    volumes:
      - ./backend/src:/app/src
      - ./backend/src/data:/app/src/data
    restart: unless-stopped
    profiles:
      - dev
