#!/usr/bin/env python3
"""
Script para converter questões para o formato esperado pelo backend
"""

import json
import os

def convert_questions_format():
    """Converte questões para o formato esperado pelo backend"""
    
    # Carregar questões do arquivo integrado
    with open('questoes.json', 'r', encoding='utf-8') as f:
        questions = json.load(f)
    
    print(f"Carregadas {len(questions)} questões")
    
    # Extrair categorias únicas
    categorias = set()
    simulados = set()
    
    for q in questions:
        categorias.add(q.get('categoria', 'unknown'))
        for sim in q.get('simulados', []):
            simulados.add(sim)
    
    # Criar estrutura de categorias
    categorias_list = []
    for cat in sorted(categorias):
        cat_name = cat.replace('_', ' ').title()
        categorias_list.append({
            "id": cat,
            "nome": cat_name,
            "descricao": f"Questões sobre {cat_name.lower()}",
            "subcategorias": list(set([q.get('subcategoria', 'geral') for q in questions if q.get('categoria') == cat]))
        })
    
    # Criar estrutura de simulados
    simulados_list = []
    for sim in sorted(simulados):
        sim_questions = [q for q in questions if sim in q.get('simulados', [])]
        simulados_list.append({
            "id": sim,
            "nome": sim.replace('_', ' ').title(),
            "total_questoes": len(sim_questions),
            "descricao": f"Simulado com {len(sim_questions)} questões"
        })
    
    # Criar estrutura final
    backend_data = {
        "questoes": questions,
        "categorias": categorias_list,
        "simulados": simulados_list
    }
    
    # Salvar no formato do backend
    output_file = 'src/data/questions.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(backend_data, f, ensure_ascii=False, indent=2)
    
    print(f"Arquivo convertido salvo em: {output_file}")
    
    # Estatísticas
    print(f"\nEstatísticas:")
    print(f"  Questões: {len(questions)}")
    print(f"  Categorias: {len(categorias_list)}")
    print(f"  Simulados: {len(simulados_list)}")
    
    # Distribuição por idioma
    idiomas = {}
    for q in questions:
        idioma = q.get('idioma', 'unknown')
        idiomas[idioma] = idiomas.get(idioma, 0) + 1
    
    print(f"\nPor idioma:")
    for idioma, count in sorted(idiomas.items()):
        print(f"  {idioma}: {count} questões")

if __name__ == "__main__":
    convert_questions_format()
