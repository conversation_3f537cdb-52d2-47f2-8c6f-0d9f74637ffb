import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { 
  Clock, 
  Target, 
  CheckCircle, 
  XCircle,
  ArrowLeft,
  ArrowRight,
  SkipForward,
  Home,
  RotateCcw
} from 'lucide-react';

interface Questao {
  id: number;
  pergunta: string;
  alternativas: string[];
  resposta_correta: number;
  categoria: string;
  subcategoria: string;
  dificuldade: string;
  explicacao: string;
  simulados: string[];
}

interface SimulationSectionProps {
  questoesSimulado: Questao[];
  questaoAtual: number;
  respostas: number[];
  mostrarResultado: boolean;
  tempoRestante: number | null;
  feedbackImediato: boolean;
  respostaClicada: number | null;
  responderQuestao: (indiceResposta: number) => void;
  proximaQuestao: () => void;
  questaoAnterior: () => void;
  finalizarSimulado: () => void;
  voltarParaInicio: () => void;
  calcularResultado: () => { acertos: number; total: number; percentual: number };
}

const SimulationSection: React.FC<SimulationSectionProps> = ({
  questoesSimulado,
  questaoAtual,
  respostas,
  mostrarResultado,
  tempoRestante,
  feedbackImediato,
  respostaClicada,
  responderQuestao,
  proximaQuestao,
  questaoAnterior,
  finalizarSimulado,
  voltarParaInicio,
  calcularResultado
}) => {
  // Função para formatar tempo
  const formatarTempo = (segundos: number) => {
    const horas = Math.floor(segundos / 3600);
    const minutos = Math.floor((segundos % 3600) / 60);
    const segs = segundos % 60;
    return `${horas.toString().padStart(2, '0')}:${minutos.toString().padStart(2, '0')}:${segs.toString().padStart(2, '0')}`;
  };

  // Renderizar resultado final
  if (mostrarResultado) {
    const resultado = calcularResultado();
    const aprovado = resultado.percentual >= 65;

    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <Card className={`border-l-4 ${aprovado ? 'border-l-green-500 bg-green-50' : 'border-l-red-500 bg-red-50'}`}>
          <CardHeader className="text-center">
            <CardTitle className={`text-3xl ${aprovado ? 'text-green-800' : 'text-red-800'}`}>
              {aprovado ? (
                <>
                  <CheckCircle className="w-12 h-12 mx-auto mb-4" />
                  Parabéns! Você foi aprovado!
                </>
              ) : (
                <>
                  <XCircle className="w-12 h-12 mx-auto mb-4" />
                  Continue estudando!
                </>
              )}
            </CardTitle>
            <CardDescription className={`text-lg ${aprovado ? 'text-green-700' : 'text-red-700'}`}>
              {aprovado 
                ? 'Você atingiu a pontuação necessária para aprovação no ITIL 4'
                : 'Você precisa de pelo menos 65% para ser aprovado'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div>
                <div className="text-3xl font-bold text-gray-900">{resultado.acertos}</div>
                <div className="text-sm text-gray-600">Acertos</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-gray-900">{resultado.total}</div>
                <div className="text-sm text-gray-600">Total de Questões</div>
              </div>
              <div>
                <div className={`text-3xl font-bold ${aprovado ? 'text-green-600' : 'text-red-600'}`}>
                  {resultado.percentual}%
                </div>
                <div className="text-sm text-gray-600">Percentual</div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-center gap-4">
            <Button onClick={voltarParaInicio} variant="outline">
              <Home className="w-4 h-4 mr-2" />
              Voltar ao Início
            </Button>
            <Button onClick={() => window.location.reload()}>
              <RotateCcw className="w-4 h-4 mr-2" />
              Novo Simulado
            </Button>
          </CardFooter>
        </Card>

        {/* Revisão das Questões */}
        <Card>
          <CardHeader>
            <CardTitle>Revisão das Questões</CardTitle>
            <CardDescription>
              Analise suas respostas para identificar pontos de melhoria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {questoesSimulado.map((questao, index) => {
                const respostaUsuario = respostas[index];
                const respostaCorreta = questao.resposta_correta;
                const acertou = respostaUsuario === respostaCorreta;

                return (
                  <div key={questao.id} className={`p-4 border rounded-lg ${acertou ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-gray-900">
                        Questão {index + 1}
                      </h4>
                      {acertou ? (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      ) : (
                        <XCircle className="w-5 h-5 text-red-600" />
                      )}
                    </div>
                    <p className="text-sm text-gray-700 mb-3">{questao.pergunta}</p>
                    
                    {respostaUsuario !== -1 && (
                      <div className="space-y-1">
                        <div className={`text-sm ${acertou ? 'text-green-700' : 'text-red-700'}`}>
                          <strong>Sua resposta:</strong> {questao.alternativas[respostaUsuario]}
                        </div>
                        {!acertou && (
                          <div className="text-sm text-green-700">
                            <strong>Resposta correta:</strong> {questao.alternativas[respostaCorreta]}
                          </div>
                        )}
                      </div>
                    )}
                    
                    {respostaUsuario === -1 && (
                      <div className="text-sm text-gray-600">
                        <strong>Não respondida</strong> - Resposta correta: {questao.alternativas[respostaCorreta]}
                      </div>
                    )}
                    
                    <div className="mt-3 p-3 bg-blue-50 rounded text-sm text-blue-800">
                      <strong>Explicação:</strong> {questao.explicacao}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Renderizar questão atual
  const questao = questoesSimulado[questaoAtual];
  if (!questao) return null;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header com informações do simulado */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Questão <span className="font-semibold">{questaoAtual + 1}</span> de{' '}
                <span className="font-semibold">{questoesSimulado.length}</span>
              </div>
              <div className="text-sm text-gray-600">
                Categoria: <span className="font-semibold">{questao.categoria.replace('_', ' ')}</span>
              </div>
              <div className="text-sm text-gray-600">
                Dificuldade: <span className={`font-semibold ${
                  questao.dificuldade === 'facil' ? 'text-green-600' :
                  questao.dificuldade === 'media' ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {questao.dificuldade}
                </span>
              </div>
            </div>
            
            {tempoRestante !== null && (
              <div className={`flex items-center text-lg font-mono ${
                tempoRestante < 300 ? 'text-red-600' : 'text-gray-700'
              }`}>
                <Clock className="w-5 h-5 mr-2" />
                {formatarTempo(tempoRestante)}
              </div>
            )}
          </div>
          
          {/* Barra de progresso */}
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((questaoAtual + 1) / questoesSimulado.length) * 100}%` }}
              ></div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Questão */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl leading-relaxed">
            {questao.pergunta}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {questao.alternativas.map((alternativa, index) => {
              const isSelected = respostas[questaoAtual] === index;
              const isCorrect = index === questao.resposta_correta;
              const isClicked = respostaClicada === index;
              
              let buttonClass = "w-full text-left p-4 border rounded-lg transition-all duration-200 ";
              
              if (feedbackImediato && isClicked) {
                if (isCorrect) {
                  buttonClass += "bg-green-100 border-green-500 text-green-800";
                } else {
                  buttonClass += "bg-red-100 border-red-500 text-red-800";
                }
              } else if (isSelected) {
                buttonClass += "bg-blue-100 border-blue-500 text-blue-800";
              } else {
                buttonClass += "bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400";
              }

              return (
                <button
                  key={index}
                  onClick={() => responderQuestao(index)}
                  className={buttonClass}
                  disabled={feedbackImediato && respostaClicada !== null}
                >
                  <div className="flex items-start">
                    <span className="font-semibold mr-3 mt-1">
                      {String.fromCharCode(65 + index)}.
                    </span>
                    <span className="flex-1">{alternativa}</span>
                    {isSelected && (
                      <Target className="w-5 h-5 ml-2 mt-1" />
                    )}
                  </div>
                </button>
              );
            })}
          </div>

          {/* Feedback imediato */}
          {feedbackImediato && respostaClicada !== null && (
            <div className={`mt-6 p-4 rounded-lg ${
              respostaClicada === questao.resposta_correta 
                ? 'bg-green-100 border border-green-300' 
                : 'bg-red-100 border border-red-300'
            }`}>
              <div className={`font-semibold mb-2 ${
                respostaClicada === questao.resposta_correta ? 'text-green-800' : 'text-red-800'
              }`}>
                {respostaClicada === questao.resposta_correta ? 'Correto!' : 'Incorreto!'}
              </div>
              <p className="text-sm text-gray-700">{questao.explicacao}</p>
            </div>
          )}
        </CardContent>
        
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={questaoAnterior}
            disabled={questaoAtual === 0}
            className="border-gray-300"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Anterior
          </Button>
          
          <div className="flex gap-2">
            {!feedbackImediato && (
              <Button
                variant="outline"
                onClick={proximaQuestao}
                disabled={feedbackImediato && respostaClicada !== null}
                className="border-gray-300"
              >
                <SkipForward className="w-4 h-4 mr-2" />
                Pular
              </Button>
            )}
            
            <Button
              onClick={questaoAtual === questoesSimulado.length - 1 ? finalizarSimulado : proximaQuestao}
              disabled={feedbackImediato && respostaClicada !== null}
            >
              {questaoAtual === questoesSimulado.length - 1 ? "Finalizar" : "Próxima"}
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SimulationSection;
