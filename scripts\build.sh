#!/bin/bash

# Script de build para produção do projeto Simulado ITIL 4
# Este script compila o frontend e prepara a aplicação para deploy

set -e  # Sair em caso de erro

echo "🏗️ Iniciando build de produção..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir mensagens coloridas
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Limpar builds anteriores
clean_previous_builds() {
    print_status "Limpando builds anteriores..."
    
    # Limpar dist do frontend
    if [ -d "frontend/dist" ]; then
        rm -rf frontend/dist
        print_status "Diretório frontend/dist removido"
    fi
    
    # Limpar arquivos estáticos do backend (exceto .gitkeep)
    if [ -d "backend/src/static" ]; then
        find backend/src/static -type f ! -name '.gitkeep' -delete
        print_status "Arquivos estáticos antigos removidos"
    fi
}

# Build do frontend
build_frontend() {
    print_status "Compilando frontend..."
    
    cd frontend
    
    # Verificar se node_modules existe
    if [ ! -d "node_modules" ]; then
        print_warning "node_modules não encontrado. Instalando dependências..."
        if command -v pnpm &> /dev/null; then
            pnpm install
        elif command -v yarn &> /dev/null; then
            yarn install
        else
            npm install
        fi
    fi
    
    # Executar linting
    print_status "Executando linting..."
    if command -v pnpm &> /dev/null; then
        pnpm run lint
    elif command -v yarn &> /dev/null; then
        yarn lint
    else
        npm run lint
    fi
    
    # Executar testes
    print_status "Executando testes do frontend..."
    if command -v pnpm &> /dev/null; then
        pnpm run test --run
    elif command -v yarn &> /dev/null; then
        yarn test --run
    else
        npm run test --run
    fi
    
    # Build de produção
    print_status "Compilando para produção..."
    if command -v pnpm &> /dev/null; then
        pnpm run build
    elif command -v yarn &> /dev/null; then
        yarn build
    else
        npm run build
    fi
    
    # Verificar se o build foi criado
    if [ ! -d "dist" ]; then
        print_error "Build do frontend falhou - diretório dist não encontrado"
        exit 1
    fi
    
    print_success "Frontend compilado com sucesso!"
    cd ..
}

# Copiar arquivos para o backend
copy_to_backend() {
    print_status "Copiando arquivos para o backend..."
    
    # Criar diretório static se não existir
    mkdir -p backend/src/static
    
    # Copiar arquivos do build
    cp -r frontend/dist/* backend/src/static/
    
    # Verificar se os arquivos foram copiados
    if [ ! -f "backend/src/static/index.html" ]; then
        print_error "Falha ao copiar arquivos para o backend"
        exit 1
    fi
    
    print_success "Arquivos copiados para backend/src/static/"
}

# Executar testes do backend
test_backend() {
    print_status "Executando testes do backend..."
    
    cd backend
    
    # Verificar se ambiente virtual existe
    if [ ! -d "venv" ]; then
        print_warning "Ambiente virtual não encontrado. Criando..."
        python3 -m venv venv 2>/dev/null || python -m venv venv
    fi
    
    # Ativar ambiente virtual
    source venv/bin/activate
    
    # Instalar dependências se necessário
    if [ ! -f "venv/pyvenv.cfg" ] || [ requirements.txt -nt venv/pyvenv.cfg ]; then
        print_status "Instalando/atualizando dependências..."
        pip install -r requirements.txt
        if [ -f "requirements-dev.txt" ]; then
            pip install -r requirements-dev.txt
        fi
    fi
    
    # Executar testes
    if command -v pytest &> /dev/null; then
        pytest -v
    else
        python -m pytest -v
    fi
    
    print_success "Testes do backend executados com sucesso!"
    cd ..
}

# Gerar informações do build
generate_build_info() {
    print_status "Gerando informações do build..."
    
    BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    BUILD_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    BUILD_BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")
    
    cat > backend/src/static/build-info.json << EOF
{
  "buildDate": "$BUILD_DATE",
  "gitHash": "$BUILD_HASH",
  "gitBranch": "$BUILD_BRANCH",
  "version": "1.0.0"
}
EOF
    
    print_success "Informações do build salvas em build-info.json"
}

# Função principal
main() {
    print_status "Iniciando processo de build..."
    
    # Verificar se estamos no diretório raiz do projeto
    if [ ! -f "README.md" ] || [ ! -d "frontend" ] || [ ! -d "backend" ]; then
        print_error "Execute este script a partir do diretório raiz do projeto"
        exit 1
    fi
    
    clean_previous_builds
    build_frontend
    copy_to_backend
    test_backend
    generate_build_info
    
    print_success "🎉 Build de produção concluído com sucesso!"
    echo ""
    print_status "Arquivos prontos para deploy em: backend/src/static/"
    print_status "Para executar em produção:"
    echo "  cd backend"
    echo "  source venv/bin/activate"
    echo "  python src/main.py"
    echo ""
    print_status "Ou usando Gunicorn:"
    echo "  gunicorn -w 4 -b 0.0.0.0:5000 src.main:app"
}

# Executar função principal
main "$@"
