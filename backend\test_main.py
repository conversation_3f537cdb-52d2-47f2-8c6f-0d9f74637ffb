"""
Testes para a API do Simulado ITIL 4
"""
import pytest
import json
import os
import tempfile
from unittest.mock import patch, mock_open
import sys

# Adiciona o diretório src ao path para importar o módulo
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from main import app, load_questions, initialize_app


@pytest.fixture
def client():
    """Fixture para cliente de teste do Flask."""
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client


@pytest.fixture
def sample_data():
    """Dados de exemplo para testes."""
    return {
        "questoes": [
            {
                "id": 1,
                "pergunta": "Qual é o propósito do ITIL?",
                "alternativas": ["A", "B", "C", "D"],
                "resposta_correta": 0,
                "categoria": "conceitos_fundamentais",
                "subcategoria": "definicoes_basicas",
                "dificuldade": "facil",
                "explicacao": "Explicação da resposta",
                "simulados": ["ITIL_4_DUMP_1"]
            }
        ],
        "categorias": [
            {
                "id": "conceitos_fundamentais",
                "nome": "Conceitos Fundamentais",
                "descricao": "Conceitos básicos do ITIL",
                "subcategorias": ["definicoes_basicas"]
            }
        ],
        "simulados": [
            {
                "id": "ITIL_4_DUMP_1",
                "nome": "ITIL 4 DUMP 1",
                "total_questoes": 1,
                "descricao": "Simulado de teste"
            }
        ]
    }


class TestHealthCheck:
    """Testes para o endpoint de health check."""
    
    def test_health_check(self, client):
        """Testa se o endpoint de health check retorna status correto."""
        response = client.get('/api/health')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['status'] == 'healthy'
        assert 'message' in data
        assert 'version' in data


class TestLoadQuestions:
    """Testes para a função load_questions."""
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('os.path.join')
    def test_load_questions_success(self, mock_join, mock_file, sample_data):
        """Testa carregamento bem-sucedido de questões."""
        mock_join.return_value = 'fake_path.json'
        mock_file.return_value.read.return_value = json.dumps(sample_data)
        
        with patch('json.load', return_value=sample_data):
            result = load_questions()
            
        assert result == sample_data
        assert len(result['questoes']) == 1
        assert len(result['categorias']) == 1
        assert len(result['simulados']) == 1
    
    @patch('builtins.open', side_effect=FileNotFoundError)
    @patch('os.path.join')
    def test_load_questions_file_not_found(self, mock_join, mock_file):
        """Testa comportamento quando arquivo não é encontrado."""
        mock_join.return_value = 'fake_path.json'
        
        result = load_questions()
        
        assert result == {"questoes": [], "categorias": [], "simulados": []}
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('os.path.join')
    @patch('json.load', side_effect=json.JSONDecodeError("Erro", "doc", 0))
    def test_load_questions_json_decode_error(self, mock_json, mock_join, mock_file):
        """Testa comportamento quando há erro de decodificação JSON."""
        mock_join.return_value = 'fake_path.json'
        
        result = load_questions()
        
        assert result == {"questoes": [], "categorias": [], "simulados": []}


class TestAPIEndpoints:
    """Testes para os endpoints da API."""
    
    @patch('main.load_questions')
    def test_get_questions(self, mock_load, client, sample_data):
        """Testa endpoint de buscar todas as questões."""
        mock_load.return_value = sample_data
        
        response = client.get('/api/questions')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data['questoes']) == 1
        assert len(data['categorias']) == 1
        assert len(data['simulados']) == 1
    
    @patch('main.load_questions')
    def test_get_questions_by_category(self, mock_load, client, sample_data):
        """Testa endpoint de buscar questões por categoria."""
        mock_load.return_value = sample_data
        
        response = client.get('/api/questions/category/conceitos_fundamentais')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data) == 1
        assert data[0]['categoria'] == 'conceitos_fundamentais'
    
    @patch('main.load_questions')
    def test_get_questions_by_category_not_found(self, mock_load, client, sample_data):
        """Testa endpoint quando categoria não existe."""
        mock_load.return_value = sample_data
        
        response = client.get('/api/questions/category/categoria_inexistente')
        assert response.status_code == 404
        
        data = json.loads(response.data)
        assert 'message' in data
    
    @patch('main.load_questions')
    def test_get_questions_by_simulado(self, mock_load, client, sample_data):
        """Testa endpoint de buscar questões por simulado."""
        mock_load.return_value = sample_data
        
        response = client.get('/api/questions/simulado/ITIL_4_DUMP_1')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data) == 1
        assert 'ITIL_4_DUMP_1' in data[0]['simulados']
    
    @patch('main.load_questions')
    def test_get_random_questions(self, mock_load, client, sample_data):
        """Testa endpoint de buscar questões aleatórias."""
        mock_load.return_value = sample_data
        
        response = client.get('/api/questions/random/1')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data) == 1
    
    @patch('main.load_questions')
    def test_get_random_questions_invalid_count(self, mock_load, client, sample_data):
        """Testa endpoint com número inválido de questões."""
        mock_load.return_value = sample_data
        
        response = client.get('/api/questions/random/0')
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert 'error' in data
    
    @patch('main.load_questions')
    def test_get_categories(self, mock_load, client, sample_data):
        """Testa endpoint de buscar categorias."""
        mock_load.return_value = sample_data
        
        response = client.get('/api/categories')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data) == 1
        assert data[0]['id'] == 'conceitos_fundamentais'
    
    @patch('main.load_questions')
    def test_get_simulados(self, mock_load, client, sample_data):
        """Testa endpoint de buscar simulados."""
        mock_load.return_value = sample_data
        
        response = client.get('/api/simulados')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert len(data) == 1
        assert data[0]['id'] == 'ITIL_4_DUMP_1'


class TestErrorHandling:
    """Testes para tratamento de erros."""
    
    def test_404_error(self, client):
        """Testa tratamento de erro 404."""
        response = client.get('/api/endpoint_inexistente')
        assert response.status_code == 404
        
        data = json.loads(response.data)
        assert 'error' in data
    
    @patch('main.load_questions', side_effect=Exception("Erro de teste"))
    def test_500_error(self, mock_load, client):
        """Testa tratamento de erro 500."""
        response = client.get('/api/questions')
        assert response.status_code == 500
        
        data = json.loads(response.data)
        assert 'error' in data


if __name__ == '__main__':
    pytest.main([__file__])
