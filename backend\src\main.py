import sys
import os
# Definir __file__ se não estiver definido (para execução via exec)
if '__file__' not in globals():
    __file__ = os.path.abspath('main.py')
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))  # DON'T CHANGE THIS !!!

from flask import Flask, jsonify, request
from flask_cors import CORS
import json
import random
import logging
from typing import Dict, Any, List
from dotenv import load_dotenv

# Carregar variáveis de ambiente
try:
    load_dotenv()
except (AssertionError, AttributeError):
    # Fallback para quando load_dotenv falha (ex: execução via exec())
    import os
    env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env')
    if os.path.exists(env_path):
        load_dotenv(env_path)
    else:
        print("Arquivo .env não encontrado, usando configurações padrão")

app = Flask(__name__)
CORS(app)  # Habilita CORS para todas as rotas

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configurações da aplicação
app.config['DEBUG'] = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
app.config['HOST'] = os.getenv('FLASK_HOST', '0.0.0.0')
app.config['PORT'] = int(os.getenv('FLASK_PORT', '5000'))

# Carrega as questões do arquivo JSON
def load_questions() -> Dict[str, Any]:
    """
    Carrega as questões do arquivo JSON.

    Returns:
        Dict contendo questões, categorias e simulados
    """
    questions_file = os.path.join(os.path.dirname(__file__), 'data', 'questions.json')

    try:
        with open(questions_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            logger.info(f"Questões carregadas com sucesso: {len(data.get('questoes', []))} questões")
            return data
    except FileNotFoundError:
        logger.error(f"Arquivo de questões não encontrado: {questions_file}")
        return {"questoes": [], "categorias": [], "simulados": []}
    except json.JSONDecodeError as e:
        logger.error(f"Erro ao decodificar JSON: {e}")
        return {"questoes": [], "categorias": [], "simulados": []}
    except Exception as e:
        logger.error(f"Erro inesperado ao carregar questões: {e}")
        return {"questoes": [], "categorias": [], "simulados": []}

# Rotas da API
@app.route('/api/health', methods=['GET'])
def health_check():
    """Endpoint para verificar se a API está funcionando."""
    return jsonify({
        "status": "healthy",
        "message": "API do ITILPrep Pro está funcionando",
        "version": "1.0.0"
    })

@app.route('/api/questions', methods=['GET'])
def get_questions():
    """Retorna todas as questões, categorias e simulados."""
    try:
        data = load_questions()
        return jsonify(data)
    except Exception as e:
        logger.error(f"Erro ao buscar questões: {e}")
        return jsonify({"error": "Erro interno do servidor"}), 500

@app.route('/api/questions/category/<category>', methods=['GET'])
def get_questions_by_category(category: str):
    """Retorna questões filtradas por categoria."""
    try:
        data = load_questions()
        filtered_questions = [q for q in data['questoes'] if q.get('categoria') == category]

        if not filtered_questions:
            return jsonify({"message": f"Nenhuma questão encontrada para a categoria: {category}"}), 404

        logger.info(f"Retornando {len(filtered_questions)} questões da categoria: {category}")
        return jsonify(filtered_questions)
    except Exception as e:
        logger.error(f"Erro ao buscar questões por categoria: {e}")
        return jsonify({"error": "Erro interno do servidor"}), 500

@app.route('/api/questions/simulado/<simulado>', methods=['GET'])
def get_questions_by_simulado(simulado: str):
    """Retorna questões filtradas por simulado."""
    try:
        data = load_questions()
        filtered_questions = [q for q in data['questoes'] if simulado in q.get('simulados', [])]

        if not filtered_questions:
            return jsonify({"message": f"Nenhuma questão encontrada para o simulado: {simulado}"}), 404

        logger.info(f"Retornando {len(filtered_questions)} questões do simulado: {simulado}")
        return jsonify(filtered_questions)
    except Exception as e:
        logger.error(f"Erro ao buscar questões por simulado: {e}")
        return jsonify({"error": "Erro interno do servidor"}), 500

@app.route('/api/questions/random/<int:count>', methods=['GET'])
def get_random_questions(count: int):
    """Retorna questões aleatórias."""
    try:
        if count <= 0:
            return jsonify({"error": "O número de questões deve ser maior que zero"}), 400

        # Parâmetro opcional de idioma
        language = request.args.get('lang', 'pt-br')

        data = load_questions()
        available_questions = data['questoes']

        # Filtra por idioma se especificado
        if language and language != 'all':
            available_questions = [q for q in available_questions if q.get('idioma', 'pt-br') == language]

        if not available_questions:
            return jsonify({"message": f"Nenhuma questão disponível para o idioma: {language}"}), 404

        if count > len(available_questions):
            count = len(available_questions)

        random_questions = random.sample(available_questions, count)
        logger.info(f"Retornando {len(random_questions)} questões aleatórias (idioma: {language})")
        return jsonify(random_questions)
    except Exception as e:
        logger.error(f"Erro ao buscar questões aleatórias: {e}")
        return jsonify({"error": "Erro interno do servidor"}), 500

@app.route('/api/categories', methods=['GET'])
def get_categories():
    """Retorna todas as categorias disponíveis."""
    try:
        data = load_questions()
        return jsonify(data['categorias'])
    except Exception as e:
        logger.error(f"Erro ao buscar categorias: {e}")
        return jsonify({"error": "Erro interno do servidor"}), 500

@app.route('/api/simulados', methods=['GET'])
def get_simulados():
    """Retorna todos os simulados disponíveis."""
    try:
        data = load_questions()
        simulados = data.get('simulados', [])
        logger.info(f"Retornando {len(simulados)} simulados")
        return jsonify(simulados)
    except Exception as e:
        logger.error(f"Erro ao buscar simulados: {e}")
        return jsonify({"error": "Erro interno do servidor"}), 500

@app.route('/api/languages', methods=['GET'])
def get_languages():
    """Retorna todos os idiomas disponíveis."""
    try:
        data = load_questions()
        questoes = data.get('questoes', [])

        # Extrai idiomas únicos das questões
        languages = set()
        for questao in questoes:
            lang = questao.get('idioma', 'pt-br')
            languages.add(lang)

        # Mapeia códigos de idioma para nomes
        language_names = {
            'pt-br': 'Português (Brasil)',
            'en': 'English',
            'es': 'Español'
        }

        language_list = []
        for lang_code in sorted(languages):
            language_list.append({
                'code': lang_code,
                'name': language_names.get(lang_code, lang_code.upper()),
                'count': len([q for q in questoes if q.get('idioma', 'pt-br') == lang_code])
            })

        logger.info(f"Retornando {len(language_list)} idiomas disponíveis")
        return jsonify(language_list)
    except Exception as e:
        logger.error(f"Erro ao buscar idiomas: {e}")
        return jsonify({"error": "Erro interno do servidor"}), 500

# Tratamento de erros globais
@app.errorhandler(404)
def not_found(error):
    """Tratamento para rotas não encontradas."""
    return jsonify({"error": "Endpoint não encontrado"}), 404

@app.errorhandler(500)
def internal_error(error):
    """Tratamento para erros internos do servidor."""
    logger.error(f"Erro interno do servidor: {error}")
    return jsonify({"error": "Erro interno do servidor"}), 500

# Rota para servir o frontend
# Removido: rotas para servir frontend - o Vite serve o frontend em desenvolvimento

def initialize_app():
    """Inicializa a aplicação criando diretórios e arquivos necessários."""
    # Certifique-se de que o diretório de dados existe
    data_dir = os.path.join(os.path.dirname(__file__), 'data')
    os.makedirs(data_dir, exist_ok=True)

    # Verifica se o arquivo de questões existe, se não, cria um arquivo vazio
    questions_file = os.path.join(data_dir, 'questions.json')
    if not os.path.exists(questions_file):
        logger.info("Criando arquivo de questões inicial...")
        default_data = {
            "questoes": [],
            "categorias": [],
            "simulados": []
        }
        with open(questions_file, 'w', encoding='utf-8') as f:
            json.dump(default_data, f, ensure_ascii=False, indent=2)
        logger.info(f"Arquivo de questões criado: {questions_file}")

if __name__ == '__main__':
    initialize_app()

    host = app.config['HOST']
    port = app.config['PORT']
    debug = app.config['DEBUG']

    logger.info(f"Iniciando servidor Flask em {host}:{port} (debug={debug})")
    app.run(host=host, port=port, debug=debug)
