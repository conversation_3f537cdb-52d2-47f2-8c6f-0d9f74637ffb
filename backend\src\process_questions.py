#!/usr/bin/env python3
"""
Script para processar e integrar questões ITIL 4 de múltiplas fontes.
Processa questões do arquivo ITIL-DUMP.txt e do site Daypo.
"""

import json
import re
import os
import sys
from typing import Dict, List, Any
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ITILQuestionProcessor:
    def __init__(self):
        self.questions = []
        self.categories = []
        self.simulados = []
        self.question_id_counter = 1
        
        # Mapeamento de categorias
        self.category_mapping = {
            'conceitos_fundamentais': {
                'nome': 'Conceitos Fundamentais do ITIL',
                'descricao': 'Definições básicas, Sistema de Valor de Serviço e Cadeia de Valor de Serviço',
                'subcategorias': ['definicoes_basicas', 'sistema_valor_servico', 'cadeia_valor_servico', 'papeis_responsabilidades']
            },
            'principios_orientadores': {
                'nome': 'Princípios Orientadores',
                'descricao': 'Os 7 princípios orientadores do ITIL 4',
                'subcategorias': ['foco_valor', 'comecar_onde_esta', 'progredir_iterativamente', 'colaborar_promover_visibilidade', 'pensar_trabalhar_holisticamente', 'manter_simples_pratico', 'otimizar_automatizar']
            },
            'quatro_dimensoes': {
                'nome': 'Quatro Dimensões',
                'descricao': 'Organizações e pessoas, Informação e tecnologia, Parceiros e fornecedores, Fluxos de valor e processos',
                'subcategorias': ['organizacoes_pessoas', 'informacao_tecnologia', 'parceiros_fornecedores', 'fluxos_valor_processos']
            },
            'praticas_gerenciamento': {
                'nome': 'Práticas de Gerenciamento',
                'descricao': 'Central de serviço, Gerenciamento de incidente, problema, requisição, etc.',
                'subcategorias': ['central_servico', 'gerenciamento_incidente', 'gerenciamento_problema', 'gerenciamento_requisicao_servico', 'gerenciamento_nivel_servico', 'habilitacao_mudanca', 'gerenciamento_configuracao_servico', 'gerenciamento_liberacao', 'monitoramento_gerenciamento_eventos', 'gerenciamento_seguranca_informacao', 'gerenciamento_relacionamento', 'gerenciamento_fornecedor', 'gerenciamento_implantacao', 'gerenciamento_ativo_ti']
            },
            'melhoria_continua': {
                'nome': 'Melhoria Contínua',
                'descricao': 'Modelo de melhoria contínua e práticas relacionadas',
                'subcategorias': ['modelo_melhoria_continua', 'registro_melhoria_continua', 'metricas_medicoes']
            }
        }

    def categorize_question(self, question_text: str, options: List[str]) -> tuple:
        """Categoriza uma questão baseada no conteúdo."""
        text_lower = question_text.lower()
        
        # Palavras-chave para categorização
        if any(keyword in text_lower for keyword in ['princípio', 'principio', 'orientador', 'foco no valor', 'colaborar', 'otimizar', 'automatizar', 'iterativamente', 'holístico', 'simples', 'prático']):
            return 'principios_orientadores', 'foco_valor'
        elif any(keyword in text_lower for keyword in ['dimensão', 'dimensao', 'organizações', 'pessoas', 'informação', 'tecnologia', 'parceiros', 'fornecedores', 'fluxos', 'processos']):
            return 'quatro_dimensoes', 'organizacoes_pessoas'
        elif any(keyword in text_lower for keyword in ['central de serviço', 'incidente', 'problema', 'requisição', 'mudança', 'liberação', 'configuração', 'nível de serviço']):
            return 'praticas_gerenciamento', 'central_servico'
        elif any(keyword in text_lower for keyword in ['melhoria', 'continua', 'modelo', 'registro']):
            return 'melhoria_continua', 'modelo_melhoria_continua'
        else:
            return 'conceitos_fundamentais', 'definicoes_basicas'

    def determine_difficulty(self, question_text: str, options: List[str]) -> str:
        """Determina a dificuldade da questão."""
        text_lower = question_text.lower()
        
        # Questões com palavras complexas são mais difíceis
        complex_keywords = ['análise', 'avaliação', 'implementação', 'estratégico', 'tático', 'operacional']
        if any(keyword in text_lower for keyword in complex_keywords):
            return 'dificil'
        elif len(question_text) > 200:  # Questões longas tendem a ser médias
            return 'media'
        else:
            return 'facil'

    def clean_text(self, text: str) -> str:
        """Limpa e normaliza texto."""
        if not text:
            return ""
        
        # Remove caracteres especiais e normaliza espaços
        text = re.sub(r'\s+', ' ', text.strip())
        text = text.replace('\\u00e1', 'á').replace('\\u00e9', 'é').replace('\\u00ed', 'í')
        text = text.replace('\\u00f3', 'ó').replace('\\u00fa', 'ú').replace('\\u00e7', 'ç')
        text = text.replace('\\u00e0', 'à').replace('\\u00ea', 'ê').replace('\\u00f4', 'ô')
        text = text.replace('\\u00e3', 'ã').replace('\\u00f5', 'õ').replace('\\u00fc', 'ü')
        
        return text

    def extract_daypo_questions(self, daypo_text: str) -> List[Dict]:
        """Extrai questões do texto do Daypo."""
        questions = []
        
        # Padrão para encontrar questões no formato do Daypo
        # As questões geralmente começam com texto e têm opções separadas por pontos
        lines = daypo_text.split('\n')
        current_question = None
        current_options = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Se a linha termina com ponto de interrogação, é provavelmente uma pergunta
            if line.endswith('?') and len(line) > 20:
                # Salva questão anterior se existir
                if current_question and current_options:
                    questions.append(self.create_question_dict(current_question, current_options))
                
                current_question = self.clean_text(line)
                current_options = []
            
            # Se a linha parece ser uma opção (começa com letra ou número)
            elif current_question and (re.match(r'^[a-d]\)', line.lower()) or re.match(r'^[1-4]\.', line)):
                option_text = re.sub(r'^[a-d]\)\s*', '', line, flags=re.IGNORECASE)
                option_text = re.sub(r'^[1-4]\.\s*', '', option_text)
                if option_text:
                    current_options.append(self.clean_text(option_text))
        
        # Adiciona última questão se existir
        if current_question and current_options:
            questions.append(self.create_question_dict(current_question, current_options))
        
        return questions

    def create_question_dict(self, question_text: str, options: List[str], correct_answer: int = 0, explanation: str = "") -> Dict:
        """Cria dicionário de questão padronizado."""
        category, subcategory = self.categorize_question(question_text, options)
        
        question_dict = {
            'id': self.question_id_counter,
            'pergunta': question_text,
            'alternativas': options[:4],  # Máximo 4 opções
            'resposta_correta': correct_answer,
            'categoria': category,
            'subcategoria': subcategory,
            'dificuldade': self.determine_difficulty(question_text, options),
            'explicacao': explanation or f"Questão sobre {category.replace('_', ' ')}",
            'simulados': ['ITIL_4_TODAS_07_2023'],
            'idioma': 'pt-br'
        }
        
        self.question_id_counter += 1
        return question_dict

    def process_itil_dump_file(self, file_path: str) -> List[Dict]:
        """Processa arquivo ITIL-DUMP.txt."""
        questions = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Processa questões comentadas do arquivo
            question_blocks = re.split(r'\*\s*\*\*Questão \d+:\*\*', content)
            
            for block in question_blocks[1:]:  # Pula o primeiro bloco (cabeçalho)
                question_data = self.parse_question_block(block)
                if question_data:
                    questions.append(question_data)
                    
        except Exception as e:
            logger.error(f"Erro ao processar arquivo ITIL-DUMP: {e}")
        
        return questions

    def parse_question_block(self, block: str) -> Dict:
        """Analisa um bloco de questão do ITIL-DUMP."""
        lines = block.strip().split('\n')
        if len(lines) < 6:
            return None
        
        # Extrai pergunta (primeira linha não vazia)
        question_text = ""
        options = []
        explanation = ""
        correct_answer = 0
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            if not question_text and '?' in line:
                question_text = self.clean_text(line)
            elif line.startswith('*   a)') or line.startswith('*   b)') or line.startswith('*   c)') or line.startswith('*   d)'):
                option_text = re.sub(r'\*\s*[a-d]\)\s*', '', line)
                options.append(self.clean_text(option_text))
            elif 'Resultado/Comentário' in line or 'resposta correta' in line.lower():
                # Extrai explicação
                explanation_start = i
                explanation_lines = lines[explanation_start:]
                explanation = ' '.join([l.strip() for l in explanation_lines if l.strip()])
                explanation = self.clean_text(explanation)
                
                # Tenta encontrar resposta correta
                if 'letra **a)' in explanation.lower():
                    correct_answer = 0
                elif 'letra **b)' in explanation.lower():
                    correct_answer = 1
                elif 'letra **c)' in explanation.lower():
                    correct_answer = 2
                elif 'letra **d)' in explanation.lower():
                    correct_answer = 3
                break
        
        if question_text and len(options) >= 4:
            return self.create_question_dict(question_text, options, correct_answer, explanation)
        
        return None

    def setup_categories_and_simulados(self):
        """Configura categorias e simulados."""
        # Adiciona categorias
        for cat_id, cat_info in self.category_mapping.items():
            self.categories.append({
                'id': cat_id,
                'nome': cat_info['nome'],
                'descricao': cat_info['descricao'],
                'subcategorias': cat_info['subcategorias']
            })
        
        # Adiciona simulados
        self.simulados.extend([
            {
                'id': 'ITIL_4_TODAS_07_2023',
                'nome': 'ITIL 4 TODAS 07-2023',
                'total_questoes': 239,
                'descricao': 'Compilação completa de questões ITIL 4 - Daypo 2023'
            },
            {
                'id': 'ITIL_4_DUMP_COMENTADO',
                'nome': 'ITIL 4 DUMP Comentado',
                'total_questoes': 40,
                'descricao': 'Questões ITIL 4 com explicações detalhadas'
            }
        ])

    def save_questions_database(self, output_path: str):
        """Salva banco de dados de questões."""
        database = {
            'questoes': self.questions,
            'categorias': self.categories,
            'simulados': self.simulados,
            'metadata': {
                'total_questoes': len(self.questions),
                'versao': '2.0',
                'ultima_atualizacao': '2025-06-01',
                'idiomas_suportados': ['pt-br', 'en', 'es']
            }
        }
        
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(database, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Banco de dados salvo: {output_path}")
        logger.info(f"Total de questões: {len(self.questions)}")
        logger.info(f"Total de categorias: {len(self.categories)}")
        logger.info(f"Total de simulados: {len(self.simulados)}")

def main():
    """Função principal."""
    processor = ITILQuestionProcessor()
    
    # Configura categorias e simulados
    processor.setup_categories_and_simulados()
    
    # Processa arquivo ITIL-DUMP.txt
    dump_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'ITIL-DUMP.txt')
    if os.path.exists(dump_file_path):
        logger.info("Processando arquivo ITIL-DUMP.txt...")
        dump_questions = processor.process_itil_dump_file(dump_file_path)
        processor.questions.extend(dump_questions)
        logger.info(f"Adicionadas {len(dump_questions)} questões do ITIL-DUMP")
    
    # Carrega questões existentes para preservar
    existing_questions_path = os.path.join(os.path.dirname(__file__), 'data', 'questions.json')
    if os.path.exists(existing_questions_path):
        try:
            with open(existing_questions_path, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
            
            # Adiciona questões existentes (evita duplicatas por ID)
            existing_ids = {q['id'] for q in processor.questions}
            for q in existing_data.get('questoes', []):
                if q['id'] not in existing_ids:
                    processor.questions.append(q)
            
            logger.info(f"Preservadas questões existentes")
        except Exception as e:
            logger.error(f"Erro ao carregar questões existentes: {e}")
    
    # Salva banco de dados atualizado
    output_path = os.path.join(os.path.dirname(__file__), 'data', 'questions.json')
    processor.save_questions_database(output_path)

if __name__ == '__main__':
    main()
