# Dockerfile para o Simulado ITIL 4
# Build multi-stage para otimizar o tamanho da imagem final

# Estágio 1: Build do Frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend

# Copiar arquivos de configuração
COPY frontend/package*.json ./
COPY frontend/tsconfig*.json ./
COPY frontend/vite.config.ts ./
COPY frontend/tailwind.config.js ./
COPY frontend/postcss.config.js ./
COPY frontend/eslint.config.js ./

# Instalar dependências
RUN npm ci --only=production

# Copiar código fonte
COPY frontend/src ./src
COPY frontend/index.html ./

# Build de produção
RUN npm run build

# Estágio 2: Preparar Backend
FROM python:3.11-slim AS backend

# Definir variáveis de ambiente
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_ENV=production \
    FLASK_DEBUG=False

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Criar usuário não-root
RUN useradd --create-home --shell /bin/bash app

# Definir diretório de trabalho
WORKDIR /app

# Copiar e instalar dependências Python
COPY backend/requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copiar código do backend
COPY backend/src ./src
COPY backend/.env.example ./.env

# Copiar build do frontend para o diretório static
COPY --from=frontend-builder /app/frontend/dist ./src/static

# Criar diretório de dados e definir permissões
RUN mkdir -p src/data && \
    chown -R app:app /app

# Mudar para usuário não-root
USER app

# Expor porta
EXPOSE 5000

# Comando de inicialização
CMD ["python", "src/main.py"]
