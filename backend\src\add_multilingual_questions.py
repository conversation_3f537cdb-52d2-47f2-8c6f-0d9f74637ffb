#!/usr/bin/env python3
"""
Script para adicionar questões ITIL 4 em múltiplos idiomas ao banco de dados.
"""

import json
import os
from typing import List, Dict

def add_multilingual_questions():
    """Adiciona questões em inglês e espanhol ao banco de dados."""
    
    # Questões ITIL 4 em espanhol
    spanish_questions = [
        {
            "id": 4000,
            "pergunta": "¿Cuál es el propósito de la práctica 'mesa de servicios'?",
            "alternativas": [
                "Capturar la demanda de resolución de incidentes y solicitudes de servicio",
                "Reducir la probabilidad e impacto de incidentes identificando causas reales y potenciales",
                "Maximizar el número de cambios exitosos de TI asegurando que los riesgos sean evaluados adecuadamente",
                "Establecer objetivos claros basados en el negocio para los niveles de servicio"
            ],
            "resposta_correta": 0,
            "categoria": "praticas_gerenciamento",
            "subcategoria": "central_servicos",
            "dificuldade": "facil",
            "explicacao": "La práctica de mesa de servicios sirve como el punto único de contacto entre el proveedor de servicios y los usuarios. Su propósito es capturar la demanda de resolución de incidentes y solicitudes de servicio.",
            "simulados": ["ITIL_4_SPANISH_COLLECTION"],
            "idioma": "es",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 4001,
            "pergunta": "¿Cuál es el objetivo principal del Sistema de Valor de Servicio (SVS) de ITIL 4?",
            "alternativas": [
                "Facilitar la creación de valor a través de servicios habilitados por TI",
                "Reducir los costos operacionales de la organización",
                "Implementar procesos de gobernanza de TI",
                "Automatizar todos los servicios de TI"
            ],
            "resposta_correta": 0,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "sistema_valor_servico",
            "dificuldade": "facil",
            "explicacao": "El SVS representa cómo todos los componentes y actividades de la organización trabajan juntos para facilitar la creación de valor a través de servicios habilitados por TI.",
            "simulados": ["ITIL_4_SPANISH_COLLECTION"],
            "idioma": "es",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 4002,
            "pergunta": "¿Cuántos son los principios rectores de ITIL 4?",
            "alternativas": [
                "5 principios",
                "6 principios",
                "7 principios",
                "8 principios"
            ],
            "resposta_correta": 2,
            "categoria": "principios_orientadores",
            "subcategoria": "geral",
            "dificuldade": "facil",
            "explicacao": "ITIL 4 tiene 7 principios rectores que guían las decisiones y acciones de la organización.",
            "simulados": ["ITIL_4_SPANISH_COLLECTION"],
            "idioma": "es",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 4003,
            "pergunta": "¿Cuál de las siguientes NO es una de las cuatro dimensiones de la gestión de servicios?",
            "alternativas": [
                "Organizaciones y personas",
                "Información y tecnología",
                "Socios y proveedores",
                "Costos y beneficios"
            ],
            "resposta_correta": 3,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "quatro_dimensoes",
            "dificuldade": "media",
            "explicacao": "Las cuatro dimensiones son: Organizaciones y personas, Información y tecnología, Socios y proveedores, y Flujos de valor y procesos.",
            "simulados": ["ITIL_4_SPANISH_COLLECTION"],
            "idioma": "es",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 4004,
            "pergunta": "¿Qué significa 'Utilidad' en el contexto de servicios ITIL 4?",
            "alternativas": [
                "Cómo se entrega el servicio",
                "Lo que hace el servicio",
                "Cuándo está disponible el servicio",
                "Dónde se ejecuta el servicio"
            ],
            "resposta_correta": 1,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "valor_servicos",
            "dificuldade": "media",
            "explicacao": "La utilidad se refiere a la funcionalidad ofrecida por un producto o servicio para satisfacer una necesidad específica - es decir, 'lo que hace el servicio'.",
            "simulados": ["ITIL_4_SPANISH_COLLECTION"],
            "idioma": "es",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 4005,
            "pergunta": "¿Qué práctica es responsable de minimizar el impacto negativo de los incidentes?",
            "alternativas": [
                "Gestión de Problemas",
                "Gestión de Incidentes",
                "Gestión de Cambios",
                "Mesa de Servicios"
            ],
            "resposta_correta": 1,
            "categoria": "praticas_gerenciamento",
            "subcategoria": "praticas_gerais",
            "dificuldade": "facil",
            "explicacao": "La Gestión de Incidentes tiene como objetivo minimizar el impacto negativo de los incidentes restaurando la operación normal del servicio lo más rápido posible.",
            "simulados": ["ITIL_4_SPANISH_COLLECTION"],
            "idioma": "es",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 4006,
            "pergunta": "¿Cuál es la definición de 'Servicio' en ITIL 4?",
            "alternativas": [
                "Un medio de entregar valor a los clientes facilitando los resultados que los clientes quieren lograr",
                "Un conjunto de procesos de TI automatizados",
                "Una aplicación de software que soporta procesos de negocio",
                "Un contrato entre proveedor y cliente de servicios"
            ],
            "resposta_correta": 0,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "definicoes_basicas",
            "dificuldade": "facil",
            "explicacao": "Un servicio es un medio de entregar valor a los clientes, facilitando los resultados que los clientes quieren lograr sin que tengan que gestionar costos y riesgos específicos.",
            "simulados": ["ITIL_4_SPANISH_COLLECTION"],
            "idioma": "es",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 4007,
            "pergunta": "¿Qué es 'Garantía' en el contexto de valor de servicio?",
            "alternativas": [
                "La funcionalidad ofrecida por el servicio",
                "La seguridad de los datos del cliente",
                "Cómo se entrega el servicio (disponibilidad, capacidad, seguridad, continuidad)",
                "El acuerdo de nivel de servicio"
            ],
            "resposta_correta": 2,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "valor_servicos",
            "dificuldade": "media",
            "explicacao": "La garantía se refiere a cómo se entrega el servicio, incluyendo aspectos como disponibilidad, capacidad, seguridad y continuidad del servicio.",
            "simulados": ["ITIL_4_SPANISH_COLLECTION"],
            "idioma": "es",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 4008,
            "pergunta": "¿Cuáles son los componentes principales del Sistema de Valor de Servicio (SVS)?",
            "alternativas": [
                "Procesos, funciones y roles",
                "Principios rectores, gobernanza, cadena de valor, prácticas, mejora continua",
                "Incidentes, problemas y cambios",
                "Personas, procesos y tecnología"
            ],
            "resposta_correta": 1,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "sistema_valor_servico",
            "dificuldade": "media",
            "explicacao": "El SVS incluye los principios rectores, gobernanza, cadena de valor de servicio, prácticas y mejora continua.",
            "simulados": ["ITIL_4_SPANISH_COLLECTION"],
            "idioma": "es",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 4009,
            "pergunta": "¿Qué principio rector enfatiza que 'todo lo que hace la organización debe mapear a valor'?",
            "alternativas": [
                "Comenzar donde estás",
                "Enfoque en el valor",
                "Progresar iterativamente con retroalimentación",
                "Colaborar y promover visibilidad"
            ],
            "resposta_correta": 1,
            "categoria": "principios_orientadores",
            "subcategoria": "foco_valor",
            "dificuldade": "facil",
            "explicacao": "El principio 'Enfoque en el valor' establece que todo lo que hace la organización debe mapear, directa o indirectamente, a valor para las partes interesadas.",
            "simulados": ["ITIL_4_SPANISH_COLLECTION"],
            "idioma": "es",
            "fonte": "ITIL 4 Foundation Official"
        }
    ]
    
    # Questões adicionais em inglês
    additional_english_questions = [
        {
            "id": 3024,
            "pergunta": "How many activities are there in the ITIL 4 service value chain?",
            "alternativas": [
                "5 activities",
                "6 activities",
                "7 activities",
                "8 activities"
            ],
            "resposta_correta": 1,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "cadeia_valor",
            "dificuldade": "facil",
            "explicacao": "The ITIL 4 service value chain has 6 activities: Plan, Improve, Engage, Design and transition, Obtain/build, Deliver and support.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 3025,
            "pergunta": "Which value chain activity ensures shared understanding of the vision?",
            "alternativas": [
                "Engage",
                "Plan",
                "Improve",
                "Design and transition"
            ],
            "resposta_correta": 1,
            "categoria": "conceitos_fundamentais",
            "subcategoria": "cadeia_valor",
            "dificuldade": "media",
            "explicacao": "The 'Plan' activity ensures shared understanding of the vision, current status and improvement direction for all products and services.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 3026,
            "pergunta": "What is the main purpose of the Service Desk?",
            "alternativas": [
                "To solve all IT problems",
                "To be the single point of contact between provider and users",
                "To manage changes in the environment",
                "To monitor service performance"
            ],
            "resposta_correta": 1,
            "categoria": "praticas_gerenciamento",
            "subcategoria": "central_servicos",
            "dificuldade": "facil",
            "explicacao": "The Service Desk is the single point of contact between the service provider and users for questions, queries and service requests.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en",
            "fonte": "ITIL 4 Foundation Official"
        },
        {
            "id": 3027,
            "pergunta": "What is the main difference between Incident and Problem?",
            "alternativas": [
                "There is no difference, they are synonymous",
                "Incident is an unplanned interruption; Problem is the cause of one or more incidents",
                "Problem is more urgent than Incident",
                "Incident affects users; Problem affects systems"
            ],
            "resposta_correta": 1,
            "categoria": "praticas_gerenciamento",
            "subcategoria": "incidentes_problemas",
            "dificuldade": "media",
            "explicacao": "An incident is an unplanned interruption or reduction in service quality. A problem is the cause of one or more incidents.",
            "simulados": ["ITIL_4_ENGLISH_COLLECTION"],
            "idioma": "en",
            "fonte": "ITIL 4 Foundation Official"
        }
    ]
    
    # Carrega banco de dados existente
    questions_file = os.path.join(os.path.dirname(__file__), 'data', 'questions.json')
    
    try:
        with open(questions_file, 'r', encoding='utf-8') as f:
            database = json.load(f)
    except FileNotFoundError:
        database = {'questoes': [], 'categorias': [], 'simulados': []}
    
    # Combina todas as novas questões
    all_new_questions = spanish_questions + additional_english_questions
    
    # Adiciona questões (evita duplicatas)
    existing_ids = {q['id'] for q in database['questoes']}
    new_questions = [q for q in all_new_questions if q['id'] not in existing_ids]
    
    database['questoes'].extend(new_questions)
    
    # Adiciona simulados se não existirem
    simulados_to_add = [
        {
            'id': 'ITIL_4_SPANISH_COLLECTION',
            'nome': 'ITIL 4 Colección Español',
            'total_questoes': len([q for q in database['questoes'] if q.get('idioma') == 'es']),
            'descricao': 'Preguntas ITIL 4 en español para preparación de certificación internacional'
        },
        {
            'id': 'ITIL_4_ENGLISH_COLLECTION',
            'nome': 'ITIL 4 English Collection',
            'total_questoes': len([q for q in database['questoes'] if q.get('idioma') == 'en']),
            'descricao': 'ITIL 4 questions in English for international certification preparation'
        }
    ]
    
    # Atualiza ou adiciona simulados
    existing_simulado_ids = {s['id'] for s in database['simulados']}
    for simulado in simulados_to_add:
        if simulado['id'] in existing_simulado_ids:
            # Atualiza simulado existente
            for i, s in enumerate(database['simulados']):
                if s['id'] == simulado['id']:
                    database['simulados'][i] = simulado
                    break
        else:
            # Adiciona novo simulado
            database['simulados'].append(simulado)
    
    # Salva banco de dados atualizado
    with open(questions_file, 'w', encoding='utf-8') as f:
        json.dump(database, f, ensure_ascii=False, indent=2)
    
    print(f"Adicionadas {len(new_questions)} questões multilíngues")
    print(f"Total de questões no banco: {len(database['questoes'])}")
    
    # Mostra estatísticas por idioma
    idiomas = {}
    for q in database['questoes']:
        lang = q.get('idioma', 'pt-br')
        idiomas[lang] = idiomas.get(lang, 0) + 1
    
    print("\nEstatísticas por idioma:")
    for lang, count in idiomas.items():
        lang_name = {'pt-br': 'Português (Brasil)', 'en': 'English', 'es': 'Español'}.get(lang, lang)
        print(f"  {lang_name}: {count} questões")

if __name__ == '__main__':
    add_multilingual_questions()
